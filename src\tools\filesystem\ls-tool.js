import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import fileService from '../utils/file-service.js';
import logger from '../../utils/logger.js';

/**
 * LSTool - Lists contents of directories
 */
export class LSTool extends BaseTool {
  constructor() {
    super({
      name: 'list_directory',
      description: 'Lists contents of directories with filtering and sorting options',
      category: ToolCategory.FILESYSTEM,
      permissionLevel: PermissionLevel.SAFE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Absolute directory path to list contents of'
          },
          ignore: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of glob patterns to ignore',
            default: []
          },
          respect_git_ignore: {
            type: 'boolean',
            description: 'Whether to respect .gitignore patterns',
            default: true
          },
          recursive: {
            type: 'boolean',
            description: 'Whether to list contents recursively',
            default: false
          },
          show_hidden: {
            type: 'boolean',
            description: 'Whether to show hidden files and directories',
            default: false
          },
          sort_by: {
            type: 'string',
            enum: ['name', 'size', 'modified', 'type'],
            description: 'Sort entries by specified field',
            default: 'name'
          },
          sort_order: {
            type: 'string',
            enum: ['asc', 'desc'],
            description: 'Sort order (ascending or descending)',
            default: 'asc'
          },
          show_metadata: {
            type: 'boolean',
            description: 'Whether to include detailed metadata for each entry',
            default: false
          },
          max_depth: {
            type: 'integer',
            description: 'Maximum recursion depth (only used with recursive=true)',
            default: 3,
            minimum: 1,
            maximum: 10
          }
        },
        required: ['path']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate path
    if (!params.path) {
      errors.push('path is required');
    } else if (typeof params.path !== 'string') {
      errors.push('path must be a string');
    }

    // Validate ignore patterns
    if (params.ignore && !Array.isArray(params.ignore)) {
      errors.push('ignore must be an array');
    }

    // Validate boolean parameters
    if (params.respect_git_ignore !== undefined && typeof params.respect_git_ignore !== 'boolean') {
      errors.push('respect_git_ignore must be a boolean');
    }

    if (params.recursive !== undefined && typeof params.recursive !== 'boolean') {
      errors.push('recursive must be a boolean');
    }

    if (params.show_hidden !== undefined && typeof params.show_hidden !== 'boolean') {
      errors.push('show_hidden must be a boolean');
    }

    // Check if path exists and is accessible
    if (params.path) {
      try {
        const resolvedPath = fileService.validatePath(params.path);
        if (!fileService.exists(resolvedPath)) {
          errors.push(`Directory does not exist: ${params.path}`);
        } else {
          const stats = fileService.getStats(resolvedPath);
          if (!stats.isDirectory()) {
            errors.push(`Path is not a directory: ${params.path}`);
          }
        }
      } catch (error) {
        errors.push(`Invalid path: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Execute directory listing
   */
  async execute(params) {
    const {
      path: dirPath,
      ignore = [],
      respect_git_ignore = true,
      recursive = false,
      show_hidden = false,
      sort_by = 'name',
      sort_order = 'asc',
      show_metadata = false,
      max_depth = 3
    } = params;

    try {
      logger.debug(`Listing directory: ${dirPath}`);

      const options = {
        ignorePatterns: ignore,
        respectGitIgnore: respect_git_ignore,
        recursive
      };

      const entries = await fileService.readDirectory(dirPath, options);

      // Filter hidden files if not requested
      let filteredEntries = show_hidden
        ? entries
        : entries.filter(entry => !entry.name.startsWith('.'));

      // Enhanced sorting
      filteredEntries = this.sortEntries(filteredEntries, sort_by, sort_order);

      // Add metadata if requested
      if (show_metadata) {
        filteredEntries = await this.addMetadata(filteredEntries);
      }

      // Format output
      const formattedEntries = filteredEntries.map(entry => {
        const indicator = entry.type === 'directory' ? '/' : '';
        const size = entry.type === 'file' ? this.formatFileSize(entry.size) : '';
        const modified = entry.modified.toISOString().split('T')[0]; // YYYY-MM-DD format

        const formatted = {
          name: entry.name + indicator,
          type: entry.type,
          size,
          modified,
          path: entry.relativePath
        };

        // Add metadata if available
        if (entry.metadata) {
          formatted.metadata = entry.metadata;
        }

        return formatted;
      });

      // Create summary
      const summary = {
        total: filteredEntries.length,
        directories: filteredEntries.filter(e => e.type === 'directory').length,
        files: filteredEntries.filter(e => e.type === 'file').length,
        path: dirPath
      };

      // Format content for LLM
      const content = this.formatDirectoryListing(formattedEntries, summary);

      return ToolResult.success(content, {
        displayContent: content,
        metadata: {
          summary,
          entries: formattedEntries,
          options: {
            recursive,
            respectGitIgnore: respect_git_ignore,
            showHidden: show_hidden,
            ignorePatterns: ignore
          }
        }
      });

    } catch (error) {
      logger.error(`Failed to list directory ${dirPath}:`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Format directory listing for display
   */
  formatDirectoryListing(entries, summary) {
    const lines = [];
    
    // Header
    lines.push(`Directory listing: ${summary.path}`);
    lines.push(`Total: ${summary.total} items (${summary.directories} directories, ${summary.files} files)`);
    lines.push('');

    if (entries.length === 0) {
      lines.push('(empty directory)');
      return lines.join('\n');
    }

    // Find column widths
    const nameWidth = Math.max(20, Math.max(...entries.map(e => e.name.length)));
    const typeWidth = 10;
    const sizeWidth = 10;
    const dateWidth = 12;

    // Header row
    const header = [
      'Name'.padEnd(nameWidth),
      'Type'.padEnd(typeWidth),
      'Size'.padEnd(sizeWidth),
      'Modified'.padEnd(dateWidth)
    ].join(' | ');
    
    lines.push(header);
    lines.push('-'.repeat(header.length));

    // Entry rows
    for (const entry of entries) {
      const row = [
        entry.name.padEnd(nameWidth),
        entry.type.padEnd(typeWidth),
        entry.size.padEnd(sizeWidth),
        entry.modified.padEnd(dateWidth)
      ].join(' | ');
      
      lines.push(row);
    }

    return lines.join('\n');
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  }

  /**
   * Sort entries by specified field and order
   */
  sortEntries(entries, sortBy, sortOrder) {
    const sortedEntries = [...entries];

    sortedEntries.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = (a.size || 0) - (b.size || 0);
          break;
        case 'modified':
          comparison = new Date(a.modified) - new Date(b.modified);
          break;
        case 'type':
          // Directories first, then files
          if (a.type !== b.type) {
            comparison = a.type === 'directory' ? -1 : 1;
          } else {
            comparison = a.name.localeCompare(b.name);
          }
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return sortedEntries;
  }

  /**
   * Add enhanced metadata to entries
   */
  async addMetadata(entries) {
    return entries.map(entry => {
      const metadata = {
        permissions: this.getPermissions(entry.path),
        isSymlink: false, // Could be enhanced to detect symlinks
        gitStatus: this.getGitStatus(entry.path),
        fileType: entry.type === 'file' ? fileService.getFileType(entry.path) : 'directory'
      };

      return {
        ...entry,
        metadata
      };
    });
  }

  /**
   * Get file permissions (simplified)
   */
  getPermissions(filePath) {
    try {
      const stats = fileService.getStats(filePath);
      const mode = stats.mode;

      // Convert to rwx format (simplified)
      const permissions = {
        readable: !!(mode & parseInt('400', 8)),
        writable: !!(mode & parseInt('200', 8)),
        executable: !!(mode & parseInt('100', 8))
      };

      return permissions;
    } catch (error) {
      return { readable: false, writable: false, executable: false };
    }
  }

  /**
   * Get git status for file (placeholder)
   */
  getGitStatus(filePath) {
    // This is a placeholder for git status integration
    // In a full implementation, this would check git status
    return 'unknown';
  }
}
