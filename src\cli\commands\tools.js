import chalk from 'chalk';
import Table from 'cli-table3';
import { getToolRegistry, getToolStats } from '../../tools/index.js';
import configManager from '../../config/config-manager.js';
import logger from '../../utils/logger.js';

/**
 * Tools command - Manage and inspect available tools
 */
export async function toolsCommand(options) {
  try {
    if (options.list) {
      await listTools();
    } else if (options.stats) {
      await showToolStats();
    } else if (options.telemetry) {
      await showTelemetryData();
    } else if (options.categories) {
      await listCategories();
    } else if (options.test) {
      await testTools();
    } else if (options.init) {
      await initializeTools();
    } else {
      await showToolsOverview();
    }
  } catch (error) {
    logger.error('Tools command failed:', error.message);
    console.log(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}

/**
 * Show tools overview
 */
async function showToolsOverview() {
  console.log(chalk.bold.blue('🔧 LLM CLI Tools System'));
  console.log();

  const config = configManager.get('tools');
  const toolsEnabled = config?.enabled !== false;

  // Tool system status
  console.log(chalk.bold('System Status:'));
  console.log(`  Tools Enabled: ${toolsEnabled ? chalk.green('✓') : chalk.red('✗')}`);
  console.log(`  Safe Mode: ${config?.safeMode ? chalk.green('✓') : chalk.yellow('✗')}`);
  console.log(`  Auto Confirm: ${config?.autoConfirm ? chalk.yellow('✓') : chalk.green('✗')}`);
  console.log();

  if (!toolsEnabled) {
    console.log(chalk.yellow('Tools are disabled. Enable them in configuration to use tool features.'));
    return;
  }

  try {
    const registry = getToolRegistry();
    if (!registry.initialized) {
      await registry.initialize();
    }

    const stats = getToolStats();

    console.log(chalk.bold('Tool Statistics:'));
    console.log(`  Total Tools: ${chalk.cyan(stats.totalTools)}`);
    console.log(`  Categories: ${chalk.cyan(Object.keys(stats.categories).length)}`);

    // Show telemetry data if available
    try {
      const telemetryData = registry.getTelemetryData ? registry.getTelemetryData() : null;
      if (telemetryData && telemetryData.telemetryEnabled) {
        console.log(`  Total Executions: ${chalk.cyan(telemetryData.global.totalExecutions)}`);
        console.log(`  Success Rate: ${chalk.cyan(100 - telemetryData.global.errorRate)}%`);
        console.log(`  Avg Execution Time: ${chalk.cyan(telemetryData.global.averageExecutionTime)}ms`);
      }
    } catch (error) {
      // Silently ignore if method doesn't exist
    }

    // Show discovered tools (if methods exist)
    try {
      const discoveredTools = registry.getDiscoveredTools ? registry.getDiscoveredTools() : [];
      const mcpServers = registry.getMCPServers ? registry.getMCPServers() : [];
      if (discoveredTools.length > 0) {
        console.log(`  Discovered Tools: ${chalk.cyan(discoveredTools.length)}`);
      }
      if (mcpServers.length > 0) {
        console.log(`  MCP Servers: ${chalk.cyan(mcpServers.length)}`);
      }
    } catch (error) {
      // Silently ignore if methods don't exist
    }
    console.log();

    // Category breakdown
    console.log(chalk.bold('Tools by Category:'));
    for (const [category, count] of Object.entries(stats.categories)) {
      console.log(`  ${category}: ${chalk.cyan(count)} tools`);
    }
    console.log();

    console.log(chalk.gray('Use --list to see all tools, --stats for detailed statistics, --telemetry for performance data'));
    
  } catch (error) {
    console.log(chalk.red('Failed to load tool system:'), error.message);
  }
}

/**
 * List all available tools
 */
async function listTools() {
  console.log(chalk.bold.blue('📋 Available Tools'));
  console.log();

  try {
    const registry = getToolRegistry();
    if (!registry.initialized) {
      await registry.initialize();
    }

    const tools = registry.getAllTools();
    
    if (tools.length === 0) {
      console.log(chalk.yellow('No tools available.'));
      return;
    }

    // Group tools by category
    const toolsByCategory = {};
    for (const tool of tools) {
      const category = tool.category || 'Other';
      if (!toolsByCategory[category]) {
        toolsByCategory[category] = [];
      }
      toolsByCategory[category].push(tool);
    }

    // Display tools by category
    for (const [category, categoryTools] of Object.entries(toolsByCategory)) {
      console.log(chalk.bold.cyan(`${category.toUpperCase()} TOOLS`));
      
      const table = new Table({
        head: ['Name', 'Description', 'Permission'],
        colWidths: [25, 50, 15]
      });

      for (const tool of categoryTools) {
        const permissionColor = tool.permissionLevel === 'safe' ? chalk.green :
                               tool.permissionLevel === 'moderate' ? chalk.yellow :
                               chalk.red;
        
        table.push([
          chalk.bold(tool.name),
          tool.description,
          permissionColor(tool.permissionLevel)
        ]);
      }

      console.log(table.toString());
      console.log();
    }

  } catch (error) {
    console.log(chalk.red('Failed to list tools:'), error.message);
  }
}

/**
 * Show detailed tool statistics
 */
async function showToolStats() {
  console.log(chalk.bold.blue('📊 Tool Statistics'));
  console.log();

  try {
    const registry = getToolRegistry();
    if (!registry.initialized) {
      await registry.initialize();
    }

    const stats = getToolStats();
    
    // Overall statistics
    console.log(chalk.bold('Overall Statistics:'));
    console.log(`  Total Tools: ${chalk.cyan(stats.totalTools)}`);
    console.log(`  Categories: ${chalk.cyan(Object.keys(stats.categories).length)}`);
    console.log();

    // Category statistics
    console.log(chalk.bold('Category Breakdown:'));
    const categoryTable = new Table({
      head: ['Category', 'Tools', 'Percentage'],
      colWidths: [20, 10, 15]
    });

    for (const [category, count] of Object.entries(stats.categories)) {
      const percentage = ((count / stats.totalTools) * 100).toFixed(1);
      categoryTable.push([
        chalk.cyan(category),
        chalk.bold(count),
        `${percentage}%`
      ]);
    }

    console.log(categoryTable.toString());
    console.log();

    // Individual tool statistics
    console.log(chalk.bold('Individual Tool Status:'));
    const toolTable = new Table({
      head: ['Tool', 'Category', 'State', 'Execution Time'],
      colWidths: [25, 15, 12, 18]
    });

    for (const [name, toolInfo] of Object.entries(stats.tools)) {
      const stateColor = toolInfo.state === 'completed' ? chalk.green :
                        toolInfo.state === 'failed' ? chalk.red :
                        toolInfo.state === 'executing' ? chalk.yellow :
                        chalk.gray;
      
      toolTable.push([
        chalk.bold(name),
        toolInfo.category,
        stateColor(toolInfo.state),
        `${toolInfo.executionTime}ms`
      ]);
    }

    console.log(toolTable.toString());

  } catch (error) {
    console.log(chalk.red('Failed to get tool statistics:'), error.message);
  }
}

/**
 * List tool categories
 */
async function listCategories() {
  console.log(chalk.bold.blue('📂 Tool Categories'));
  console.log();

  try {
    const registry = getToolRegistry();
    if (!registry.initialized) {
      await registry.initialize();
    }

    const categories = registry.getCategories();
    
    if (categories.length === 0) {
      console.log(chalk.yellow('No categories available.'));
      return;
    }

    for (const category of categories) {
      const tools = registry.getToolsByCategory(category);
      console.log(chalk.bold.cyan(`${category.toUpperCase()}`));
      console.log(chalk.gray(`  ${tools.length} tools available`));
      
      for (const tool of tools) {
        console.log(`  • ${chalk.bold(tool.name)} - ${tool.description}`);
      }
      console.log();
    }

  } catch (error) {
    console.log(chalk.red('Failed to list categories:'), error.message);
  }
}

/**
 * Test tool system
 */
async function testTools() {
  console.log(chalk.bold.blue('🧪 Testing Tool System'));
  console.log();

  try {
    const registry = getToolRegistry();
    
    console.log('Initializing tool registry...');
    if (!registry.initialized) {
      await registry.initialize();
    }
    console.log(chalk.green('✓ Tool registry initialized'));

    const tools = registry.getAllTools();
    console.log(chalk.green(`✓ Found ${tools.length} tools`));

    // Test function definitions
    console.log('Testing function definitions...');
    const functionDefs = registry.getFunctionDefinitions();
    console.log(chalk.green(`✓ Generated ${functionDefs.length} function definitions`));

    // Test tool validation (safe tools only)
    console.log('Testing tool validation...');
    let validationTests = 0;
    let validationPassed = 0;

    for (const tool of tools) {
      if (tool.permissionLevel === 'safe') {
        try {
          // Test with empty parameters
          const validation = await tool.validateParams({});
          validationTests++;
          if (!validation.isValid) {
            validationPassed++;
          }
        } catch (error) {
          console.log(chalk.yellow(`  Warning: ${tool.name} validation test failed: ${error.message}`));
        }
      }
    }

    console.log(chalk.green(`✓ Validation tests: ${validationPassed}/${validationTests} passed`));

    console.log();
    console.log(chalk.green('🎉 Tool system test completed successfully!'));

  } catch (error) {
    console.log(chalk.red('Tool system test failed:'), error.message);
  }
}

/**
 * Initialize tool system
 */
async function initializeTools() {
  console.log(chalk.bold.blue('🚀 Initializing Tool System'));
  console.log();

  try {
    const registry = getToolRegistry();
    
    console.log('Loading tools...');
    await registry.initialize();
    
    const stats = getToolStats();
    console.log(chalk.green(`✓ Loaded ${stats.totalTools} tools in ${Object.keys(stats.categories).length} categories`));

    // Update configuration
    const config = configManager.get('tools') || {};
    if (!config.rootDirectory) {
      config.rootDirectory = process.cwd();
      configManager.set('tools.rootDirectory', config.rootDirectory);
      console.log(chalk.green(`✓ Set root directory to: ${config.rootDirectory}`));
    }

    console.log();
    console.log(chalk.green('🎉 Tool system initialized successfully!'));
    console.log(chalk.gray('Use "llm-cli tools --list" to see available tools'));

  } catch (error) {
    console.log(chalk.red('Failed to initialize tool system:'), error.message);
  }
}

/**
 * Show telemetry and performance data
 */
async function showTelemetryData() {
  console.log(chalk.bold.blue('📊 Tool Telemetry & Performance Data'));
  console.log();

  try {
    const registry = getToolRegistry();
    if (!registry.initialized) {
      await registry.initialize();
    }

    const telemetryData = registry.getTelemetryData();

    if (!telemetryData.telemetryEnabled) {
      console.log(chalk.yellow('Telemetry is disabled.'));
      return;
    }

    // Global metrics
    console.log(chalk.bold('Global Metrics:'));
    console.log(`  Total Executions: ${chalk.cyan(telemetryData.global.totalExecutions)}`);
    console.log(`  Total Errors: ${chalk.red(telemetryData.global.totalErrors)}`);
    console.log(`  Success Rate: ${chalk.green((100 - telemetryData.global.errorRate).toFixed(1))}%`);
    console.log(`  Average Execution Time: ${chalk.cyan(telemetryData.global.averageExecutionTime)}ms`);
    console.log(`  Total Execution Time: ${chalk.cyan(telemetryData.global.totalExecutionTime)}ms`);
    console.log();

    // Tool-specific metrics
    console.log(chalk.bold('Tool Performance:'));

    const table = new Table({
      head: ['Tool', 'Executions', 'Errors', 'Success Rate', 'Avg Time', 'Last Used'],
      colWidths: [20, 12, 8, 12, 10, 20]
    });

    const sortedTools = Object.entries(telemetryData.tools)
      .sort(([,a], [,b]) => b.executions - a.executions);

    for (const [toolName, stats] of sortedTools) {
      if (stats.executions > 0) {
        table.push([
          toolName,
          stats.executions,
          stats.errors,
          `${(100 - stats.errorRate).toFixed(1)}%`,
          `${stats.averageTime}ms`,
          stats.lastUsed ? new Date(stats.lastUsed).toLocaleString() : 'Never'
        ]);
      }
    }

    console.log(table.toString());
    console.log();
    console.log(chalk.gray('Use "llm-cli tools --stats" for general statistics'));

  } catch (error) {
    console.log(chalk.red('Failed to load telemetry data:'), error.message);
  }
}
