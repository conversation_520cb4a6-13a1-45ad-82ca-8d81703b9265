import axios from 'axios';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import logger from '../../utils/logger.js';

/**
 * WebFetchTool - Fetches and processes content from URLs
 */
export class WebFetchTool extends BaseTool {
  constructor() {
    super({
      name: 'web_fetch',
      description: 'Fetches and processes content from URLs with multiple strategies',
      category: ToolCategory.WEB,
      permissionLevel: PermissionLevel.MODERATE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          prompt: {
            type: 'string',
            description: 'Text containing URLs and processing instructions'
          },
          extract_text_only: {
            type: 'boolean',
            description: 'Whether to extract only text content (no HTML)',
            default: true
          },
          follow_redirects: {
            type: 'boolean',
            description: 'Whether to follow HTTP redirects',
            default: true
          },
          timeout: {
            type: 'integer',
            description: 'Request timeout in milliseconds',
            minimum: 1000,
            maximum: 60000,
            default: 30000
          },
          max_content_length: {
            type: 'integer',
            description: 'Maximum content length to fetch in bytes',
            minimum: 1024,
            maximum: 10485760,
            default: 5242880
          },
          user_agent: {
            type: 'string',
            description: 'Custom User-Agent header',
            default: 'LLM-CLI-WebFetch/1.0'
          }
        },
        required: ['prompt']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate prompt
    if (!params.prompt) {
      errors.push('prompt is required');
    } else if (typeof params.prompt !== 'string') {
      errors.push('prompt must be a string');
    } else if (params.prompt.trim() === '') {
      errors.push('prompt cannot be empty');
    }

    // Validate boolean parameters
    if (params.extract_text_only !== undefined && typeof params.extract_text_only !== 'boolean') {
      errors.push('extract_text_only must be a boolean');
    }

    if (params.follow_redirects !== undefined && typeof params.follow_redirects !== 'boolean') {
      errors.push('follow_redirects must be a boolean');
    }

    // Validate numeric parameters
    if (params.timeout !== undefined) {
      if (!Number.isInteger(params.timeout) || params.timeout < 1000 || params.timeout > 60000) {
        errors.push('timeout must be an integer between 1000 and 60000 milliseconds');
      }
    }

    if (params.max_content_length !== undefined) {
      if (!Number.isInteger(params.max_content_length) || params.max_content_length < 1024 || params.max_content_length > 10485760) {
        errors.push('max_content_length must be an integer between 1024 and 10485760 bytes');
      }
    }

    // Validate user_agent
    if (params.user_agent !== undefined && typeof params.user_agent !== 'string') {
      errors.push('user_agent must be a string');
    }

    // Extract and validate URLs
    if (params.prompt) {
      const urls = this.extractUrls(params.prompt);
      if (urls.length === 0) {
        errors.push('No valid URLs found in prompt');
      } else {
        for (const url of urls) {
          if (!this.isValidUrl(url)) {
            errors.push(`Invalid URL: ${url}`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    const urls = this.extractUrls(params.prompt);
    
    // Check for potentially risky domains or protocols
    const riskyPatterns = [
      /^file:\/\//,
      /^ftp:\/\//,
      /localhost/,
      /127\.0\.0\.1/,
      /192\.168\./,
      /10\./,
      /172\.(1[6-9]|2[0-9]|3[0-1])\./
    ];

    return urls.some(url => 
      riskyPatterns.some(pattern => pattern.test(url))
    );
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    const urls = this.extractUrls(params.prompt);
    
    const lines = [];
    lines.push('Fetch content from the following URLs?');
    lines.push('');
    
    for (const url of urls) {
      lines.push(`  ${url}`);
    }
    
    if (this.isRiskyOperation(params)) {
      lines.push('');
      lines.push('⚠️  WARNING: Some URLs appear to be local or private addresses!');
    }

    return lines.join('\n');
  }

  /**
   * Execute web fetch
   */
  async execute(params) {
    const {
      prompt,
      extract_text_only = true,
      follow_redirects = true,
      timeout = 30000,
      max_content_length = 5242880, // 5MB
      user_agent = 'LLM-CLI-WebFetch/1.0'
    } = params;

    try {
      const urls = this.extractUrls(prompt);
      logger.debug(`Fetching content from ${urls.length} URLs`);

      const results = [];

      // Strategy 1: Try Gemini API with urlContext tool (if available)
      let geminiResults = null;
      try {
        geminiResults = await this.fetchWithGeminiAPI(prompt, urls);
        if (geminiResults && geminiResults.length > 0) {
          results.push(...geminiResults);
          logger.debug('Used Gemini API strategy');
        }
      } catch (error) {
        logger.debug('Gemini API fetch failed, falling back:', error.message);
      }

      // Strategy 2: Direct HTTP fetch for remaining URLs
      const remainingUrls = geminiResults
        ? urls.filter(url => !geminiResults.some(r => r.url === url))
        : urls;

      for (const url of remainingUrls) {
        try {
          const result = await this.fetchUrlDirect(url, {
            extractTextOnly: extract_text_only,
            followRedirects: follow_redirects,
            timeout,
            maxContentLength: max_content_length,
            userAgent: user_agent
          });

          results.push({
            url,
            success: true,
            strategy: 'direct',
            ...result
          });

        } catch (error) {
          logger.warn(`Failed to fetch ${url}:`, error.message);
          results.push({
            url,
            success: false,
            strategy: 'direct',
            error: error.message
          });
        }
      }

      // Create result content
      const content = this.formatFetchResults(results, prompt);
      
      // Create display content
      const displayContent = this.createDisplayContent(results, {
        originalPrompt: prompt,
        options: params
      });

      const metadata = {
        urls,
        totalUrls: urls.length,
        successfulFetches: results.filter(r => r.success).length,
        failedFetches: results.filter(r => !r.success).length,
        totalContentLength: results
          .filter(r => r.success)
          .reduce((sum, r) => sum + (r.contentLength || 0), 0),
        options: {
          extractTextOnly: extract_text_only,
          followRedirects: follow_redirects,
          timeout,
          maxContentLength: max_content_length
        }
      };

      return ToolResult.success(content, {
        displayContent,
        metadata
      });

    } catch (error) {
      logger.error('Web fetch failed:', error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Extract URLs from text
   */
  extractUrls(text) {
    const urlRegex = /https?:\/\/[^\s<>"{}|\\^`[\]]+/gi;
    const matches = text.match(urlRegex) || [];
    
    // Remove duplicates and validate
    const uniqueUrls = [...new Set(matches)];
    return uniqueUrls.filter(url => this.isValidUrl(url));
  }

  /**
   * Validate URL format
   */
  isValidUrl(string) {
    try {
      const url = new URL(string);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (error) {
      return false;
    }
  }

  /**
   * Fetch content from a single URL
   */
  async fetchUrl(url, options) {
    const {
      extractTextOnly,
      followRedirects,
      timeout,
      maxContentLength,
      userAgent
    } = options;

    const axiosConfig = {
      url,
      method: 'GET',
      timeout,
      maxRedirects: followRedirects ? 5 : 0,
      maxContentLength,
      headers: {
        'User-Agent': userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      validateStatus: (status) => status < 400 // Accept redirects and success codes
    };

    const response = await axios(axiosConfig);
    
    let content = response.data;
    let contentType = response.headers['content-type'] || '';
    
    // Process content based on type
    if (extractTextOnly && contentType.includes('text/html')) {
      content = this.extractTextFromHtml(content);
    }

    return {
      content,
      contentType,
      contentLength: content.length,
      statusCode: response.status,
      headers: response.headers,
      finalUrl: response.request.res?.responseUrl || url
    };
  }

  /**
   * Extract text content from HTML
   */
  extractTextFromHtml(html) {
    // Simple HTML to text conversion
    // Remove script and style elements
    let text = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
    text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    
    // Remove HTML tags
    text = text.replace(/<[^>]*>/g, ' ');
    
    // Decode HTML entities
    text = text.replace(/&nbsp;/g, ' ');
    text = text.replace(/&amp;/g, '&');
    text = text.replace(/&lt;/g, '<');
    text = text.replace(/&gt;/g, '>');
    text = text.replace(/&quot;/g, '"');
    text = text.replace(/&#39;/g, "'");
    
    // Clean up whitespace
    text = text.replace(/\s+/g, ' ');
    text = text.trim();
    
    return text;
  }

  /**
   * Format fetch results for LLM
   */
  formatFetchResults(results, originalPrompt) {
    const lines = [];
    
    lines.push('Web fetch results:');
    lines.push(`Original prompt: ${originalPrompt}`);
    lines.push('');

    for (const result of results) {
      lines.push(`URL: ${result.url}`);
      
      if (result.success) {
        lines.push(`Status: ${result.statusCode}`);
        lines.push(`Content-Type: ${result.contentType}`);
        lines.push(`Content Length: ${result.contentLength} characters`);
        lines.push('');
        lines.push('Content:');
        lines.push('---');
        lines.push(result.content);
        lines.push('---');
      } else {
        lines.push(`Error: ${result.error}`);
      }
      
      lines.push('');
    }

    return lines.join('\n');
  }

  /**
   * Create display content with formatting
   */
  createDisplayContent(results, context) {
    const lines = [];
    
    // Header
    lines.push('🌐 Web Fetch Results');
    lines.push(`📝 Original prompt: ${context.originalPrompt}`);
    lines.push(`📊 ${results.length} URLs processed`);
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    lines.push(`✅ ${successful} successful, ❌ ${failed} failed`);
    lines.push('');

    // Results
    for (const result of results) {
      const status = result.success ? '✅' : '❌';
      lines.push(`${status} ${result.url}`);
      
      if (result.success) {
        lines.push(`   📄 ${result.contentType}`);
        lines.push(`   📏 ${this.formatContentLength(result.contentLength)}`);
        lines.push(`   🔗 Status: ${result.statusCode}`);
        
        if (result.finalUrl !== result.url) {
          lines.push(`   🔄 Redirected to: ${result.finalUrl}`);
        }
        
        lines.push('');
        lines.push('   📝 Content:');
        lines.push('   ' + '─'.repeat(50));
        
        // Show first 500 characters of content
        const preview = result.content.length > 500 
          ? result.content.substring(0, 500) + '...'
          : result.content;
        
        const contentLines = preview.split('\n');
        for (const line of contentLines) {
          lines.push(`   ${line}`);
        }
        
        lines.push('   ' + '─'.repeat(50));
      } else {
        lines.push(`   ❌ Error: ${result.error}`);
      }
      
      lines.push('');
    }

    return lines.join('\n');
  }

  /**
   * Format content length for display
   */
  formatContentLength(length) {
    if (length < 1024) {
      return `${length} characters`;
    } else if (length < 1024 * 1024) {
      return `${(length / 1024).toFixed(1)} KB`;
    } else {
      return `${(length / (1024 * 1024)).toFixed(1)} MB`;
    }
  }

  /**
   * Fetch with Gemini API using urlContext tool
   */
  async fetchWithGeminiAPI(prompt, urls) {
    // This is a placeholder for Gemini API integration
    // In a full implementation, this would:
    // 1. Use Gemini's urlContext tool to process URLs
    // 2. Extract grounding metadata and citations
    // 3. Process response text with citation markers
    // 4. Return formatted results with source references

    // For now, return null to indicate Gemini API is not available
    return null;
  }

  /**
   * Direct URL fetch (fallback strategy)
   */
  async fetchUrlDirect(url, options = {}) {
    const {
      extractTextOnly = true,
      followRedirects = true,
      timeout = 30000,
      maxContentLength = 5242880,
      userAgent = 'LLM-CLI-WebFetch/1.0'
    } = options;

    const config = {
      method: 'GET',
      url,
      timeout,
      maxContentLength,
      maxRedirects: followRedirects ? 5 : 0,
      headers: {
        'User-Agent': userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      validateStatus: (status) => status < 400
    };

    const response = await axios(config);

    let content = response.data;

    if (extractTextOnly && typeof content === 'string') {
      // Enhanced HTML to text conversion
      content = content
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
        .replace(/<nav\b[^<]*(?:(?!<\/nav>)<[^<]*)*<\/nav>/gi, '')
        .replace(/<header\b[^<]*(?:(?!<\/header>)<[^<]*)*<\/header>/gi, '')
        .replace(/<footer\b[^<]*(?:(?!<\/footer>)<[^<]*)*<\/footer>/gi, '')
        .replace(/<aside\b[^<]*(?:(?!<\/aside>)<[^<]*)*<\/aside>/gi, '')
        .replace(/<[^>]+>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')
        .trim();
    }

    return {
      content,
      contentType: response.headers['content-type'],
      contentLength: content.length,
      statusCode: response.status,
      finalUrl: response.request.res?.responseUrl || url
    };
  }
}
