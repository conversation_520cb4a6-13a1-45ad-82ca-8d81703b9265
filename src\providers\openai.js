import { BaseProvider } from './base-provider.js';

export class OpenAIProvider extends BaseProvider {
  constructor(config) {
    super({
      ...config,
      supportsTools: true, // OpenAI supports function calling
      functionCalling: {
        enabled: true,
        maxToolCalls: 10,
        maxRecursionDepth: 5,
        parallelExecution: true, // OpenAI supports parallel function calls
        autoConfirm: config.functionCalling?.autoConfirm || false,
        safeMode: config.functionCalling?.safeMode !== false,
        timeout: config.functionCalling?.timeout || 30000
      }
    });
  }

  /**
   * Get authentication headers for OpenAI
   */
  getAuthHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Format messages for OpenAI API with enhanced tool support
   */
  formatMessages(messages) {
    return messages.map(msg => {
      const formatted = {
        role: msg.role,
        content: msg.content
      };

      // Handle tool calls in assistant messages
      if (msg.role === 'assistant' && msg.toolCalls) {
        formatted.tool_calls = msg.toolCalls.map(toolCall => ({
          id: toolCall.id,
          type: 'function',
          function: {
            name: toolCall.function.name,
            arguments: typeof toolCall.function.arguments === 'string'
              ? toolCall.function.arguments
              : JSON.stringify(toolCall.function.arguments)
          }
        }));
      }

      // Handle tool result messages
      if (msg.role === 'tool') {
        formatted.tool_call_id = msg.tool_call_id;
      }

      return formatted;
    });
  }

  /**
   * Parse OpenAI API response
   */
  parseResponse(response) {
    if (!response.choices || response.choices.length === 0) {
      throw new Error('No response choices received from OpenAI');
    }

    const choice = response.choices[0];
    const message = choice.message;

    const result = {
      content: message.content || '',
      role: message.role,
      finishReason: choice.finish_reason,
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0
      },
      model: response.model,
      provider: 'openai'
    };

    // Enhanced tool call handling
    if (message.tool_calls && message.tool_calls.length > 0) {
      result.toolCalls = message.tool_calls.map(toolCall => {
        let parsedArgs;
        try {
          parsedArgs = typeof toolCall.function.arguments === 'string'
            ? JSON.parse(toolCall.function.arguments || '{}')
            : toolCall.function.arguments || {};
        } catch (error) {
          logger.warn(`Failed to parse tool arguments for ${toolCall.function.name}:`, error.message);
          parsedArgs = {};
        }

        return {
          id: toolCall.id,
          type: toolCall.type || 'function',
          function: {
            name: toolCall.function.name,
            arguments: parsedArgs
          }
        };
      });
    }

    return result;
  }

  /**
   * Get additional parameters specific to OpenAI
   */
  getAdditionalParams(options) {
    const params = {};
    
    if (options.systemPrompt) {
      // OpenAI handles system prompts as part of messages
      // This will be handled in formatMessages if needed
    }
    
    if (options.presencePenalty !== undefined) {
      params.presence_penalty = options.presencePenalty;
    }
    
    if (options.frequencyPenalty !== undefined) {
      params.frequency_penalty = options.frequencyPenalty;
    }
    
    if (options.topP !== undefined) {
      params.top_p = options.topP;
    }
    
    if (options.stop) {
      params.stop = options.stop;
    }
    
    return params;
  }

  /**
   * Format messages with system prompt support
   */
  formatMessages(messages, systemPrompt = null) {
    const formattedMessages = [];
    
    // Add system prompt if provided
    if (systemPrompt) {
      formattedMessages.push({
        role: 'system',
        content: systemPrompt
      });
    }
    
    // Add user messages
    messages.forEach(msg => {
      formattedMessages.push({
        role: msg.role,
        content: msg.content
      });
    });
    
    return formattedMessages;
  }

  /**
   * Format tools for OpenAI API request
   */
  formatToolsForRequest(tools) {
    return tools.map(tool => {
      // Validate tool definition
      if (!tool.name || !tool.description) {
        logger.warn(`Invalid tool definition: ${JSON.stringify(tool)}`);
        return null;
      }

      return {
        type: 'function',
        function: {
          name: tool.name,
          description: tool.description,
          parameters: {
            type: 'object',
            properties: tool.parameters?.properties || {},
            required: tool.parameters?.required || [],
            additionalProperties: false
          }
        }
      };
    }).filter(Boolean); // Remove null entries
  }

  /**
   * Parse tool calls from OpenAI response
   */
  parseToolCallsFromResponse(response) {
    const choice = response.choices?.[0];
    if (!choice?.message?.tool_calls) {
      return [];
    }

    return choice.message.tool_calls.map(toolCall => ({
      id: toolCall.id,
      type: toolCall.type,
      function: {
        name: toolCall.function.name,
        arguments: JSON.parse(toolCall.function.arguments || '{}')
      }
    }));
  }

  /**
   * Send message with system prompt support
   */
  async sendMessage(messages, options = {}) {
    // Override formatMessages to include system prompt
    const originalFormatMessages = this.formatMessages;
    this.formatMessages = (msgs) => originalFormatMessages.call(this, msgs, options.systemPrompt);

    try {
      return await super.sendMessage(messages, options);
    } finally {
      // Restore original method
      this.formatMessages = originalFormatMessages;
    }
  }

  /**
   * Get OpenAI-specific tool choice options
   */
  getToolChoiceOptions() {
    return {
      auto: 'auto',        // Let the model decide
      none: 'none',        // Don't use tools
      required: 'required' // Force tool use (OpenAI specific)
    };
  }

  /**
   * Get OpenAI-specific model capabilities
   */
  getModelCapabilities(model = this.defaultModel) {
    const capabilities = {
      supportsTools: true,
      maxToolCalls: 10,
      parallelToolCalls: true,
      toolChoiceOptions: ['auto', 'none', 'required']
    };

    // Model-specific capabilities
    if (model.includes('gpt-4')) {
      capabilities.maxToolCalls = 20;
      capabilities.complexReasoning = true;
    } else if (model.includes('gpt-3.5')) {
      capabilities.maxToolCalls = 5;
      capabilities.complexReasoning = false;
    }

    return capabilities;
  }
}
