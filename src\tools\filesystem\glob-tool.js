import path from 'path';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import fileService from '../utils/file-service.js';
import logger from '../../utils/logger.js';

/**
 * GlobTool - Finds files matching glob patterns
 */
export class GlobTool extends BaseTool {
  constructor() {
    super({
      name: 'glob',
      description: 'Finds files matching glob patterns with filtering and sorting options',
      category: ToolCategory.FILESYSTEM,
      permissionLevel: PermissionLevel.SAFE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          pattern: {
            type: 'string',
            description: 'Glob pattern to match files (e.g., "**/*.js", "src/**/*.ts")'
          },
          path: {
            type: 'string',
            description: 'Directory to search in (defaults to root directory)',
            default: null
          },
          case_sensitive: {
            type: 'boolean',
            description: 'Whether pattern matching should be case sensitive',
            default: false
          },
          respect_git_ignore: {
            type: 'boolean',
            description: 'Whether to respect .gitignore patterns',
            default: true
          },
          max_results: {
            type: 'integer',
            description: 'Maximum number of results to return',
            minimum: 1,
            maximum: 1000,
            default: 100
          },
          sort_by: {
            type: 'string',
            description: 'Sort results by: name, size, modified, created',
            enum: ['name', 'size', 'modified', 'created'],
            default: 'modified'
          },
          sort_order: {
            type: 'string',
            description: 'Sort order: asc or desc',
            enum: ['asc', 'desc'],
            default: 'desc'
          }
        },
        required: ['pattern']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate pattern
    if (!params.pattern) {
      errors.push('pattern is required');
    } else if (typeof params.pattern !== 'string') {
      errors.push('pattern must be a string');
    } else if (params.pattern.trim() === '') {
      errors.push('pattern cannot be empty');
    }

    // Validate path if provided
    if (params.path !== undefined && params.path !== null) {
      if (typeof params.path !== 'string') {
        errors.push('path must be a string');
      } else {
        try {
          const resolvedPath = fileService.validatePath(params.path);
          if (!fileService.exists(resolvedPath)) {
            errors.push(`Directory does not exist: ${params.path}`);
          } else {
            const stats = fileService.getStats(resolvedPath);
            if (!stats.isDirectory()) {
              errors.push(`Path is not a directory: ${params.path}`);
            }
          }
        } catch (error) {
          errors.push(`Invalid path: ${error.message}`);
        }
      }
    }

    // Validate boolean parameters
    if (params.case_sensitive !== undefined && typeof params.case_sensitive !== 'boolean') {
      errors.push('case_sensitive must be a boolean');
    }

    if (params.respect_git_ignore !== undefined && typeof params.respect_git_ignore !== 'boolean') {
      errors.push('respect_git_ignore must be a boolean');
    }

    // Validate max_results
    if (params.max_results !== undefined) {
      if (!Number.isInteger(params.max_results) || params.max_results < 1 || params.max_results > 1000) {
        errors.push('max_results must be an integer between 1 and 1000');
      }
    }

    // Validate sort_by
    if (params.sort_by !== undefined) {
      const validSortBy = ['name', 'size', 'modified', 'created'];
      if (!validSortBy.includes(params.sort_by)) {
        errors.push(`sort_by must be one of: ${validSortBy.join(', ')}`);
      }
    }

    // Validate sort_order
    if (params.sort_order !== undefined) {
      const validSortOrder = ['asc', 'desc'];
      if (!validSortOrder.includes(params.sort_order)) {
        errors.push(`sort_order must be one of: ${validSortOrder.join(', ')}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Execute glob search
   */
  async execute(params) {
    const {
      pattern,
      path: searchPath = null,
      case_sensitive = false,
      respect_git_ignore = true,
      max_results = 100,
      sort_by = 'modified',
      sort_order = 'desc'
    } = params;

    try {
      logger.debug(`Searching for pattern: ${pattern}`);

      const searchDir = searchPath ? fileService.validatePath(searchPath) : fileService.rootDirectory;
      
      // Find files using glob pattern
      const files = await fileService.findFiles([pattern], {
        cwd: searchDir,
        respectGitIgnore: respect_git_ignore,
        caseSensitive: case_sensitive
      });

      // Get file stats for sorting
      const fileEntries = [];
      for (const filePath of files) {
        try {
          const stats = fileService.getStats(filePath);
          const relativePath = path.relative(fileService.rootDirectory, filePath);
          
          fileEntries.push({
            path: filePath,
            relativePath,
            name: path.basename(filePath),
            size: stats.size,
            modified: stats.mtime,
            created: stats.birthtime,
            extension: path.extname(filePath)
          });
        } catch (error) {
          logger.warn(`Failed to get stats for ${filePath}:`, error.message);
        }
      }

      // Sort files with enhanced sorting
      const sortedFiles = this.sortFileEntries(fileEntries, sort_by, sort_order);

      // Optimize results with better filtering
      const optimizedFiles = this.optimizeResults(sortedFiles, {
        maxResults: max_results,
        pattern,
        searchPath: searchDir
      });

      // Limit results
      const limitedFiles = optimizedFiles.slice(0, max_results);

      // Create result content
      const content = this.formatGlobResults(pattern, limitedFiles, {
        totalFound: fileEntries.length,
        searchPath: searchDir,
        maxResults: max_results
      });

      // Create display content
      const displayContent = this.createDisplayContent(pattern, limitedFiles, {
        totalFound: fileEntries.length,
        searchPath: searchDir,
        maxResults: max_results,
        sortBy: sort_by,
        sortOrder: sort_order
      });

      const metadata = {
        pattern,
        searchPath: path.relative(fileService.rootDirectory, searchDir),
        totalFound: fileEntries.length,
        returned: limitedFiles.length,
        options: {
          caseSensitive: case_sensitive,
          respectGitIgnore: respect_git_ignore,
          sortBy: sort_by,
          sortOrder: sort_order
        }
      };

      return ToolResult.success(content, {
        displayContent,
        metadata
      });

    } catch (error) {
      logger.error(`Glob search failed for pattern ${pattern}:`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Sort file entries
   */
  sortFileEntries(entries, sortBy, sortOrder) {
    const sortFunctions = {
      name: (a, b) => a.name.localeCompare(b.name),
      size: (a, b) => a.size - b.size,
      modified: (a, b) => a.modified.getTime() - b.modified.getTime(),
      created: (a, b) => a.created.getTime() - b.created.getTime()
    };

    const sortFn = sortFunctions[sortBy] || sortFunctions.modified;
    const sorted = entries.sort(sortFn);

    return sortOrder === 'desc' ? sorted.reverse() : sorted;
  }

  /**
   * Format glob results for LLM
   */
  formatGlobResults(pattern, files, summary) {
    const lines = [];
    
    lines.push(`Glob search results for pattern: ${pattern}`);
    lines.push(`Found ${summary.totalFound} files, showing ${files.length}`);
    
    if (files.length === 0) {
      lines.push('No files found matching the pattern.');
      return lines.join('\n');
    }

    lines.push('');
    lines.push('Files:');
    
    for (const file of files) {
      lines.push(`  ${file.relativePath}`);
    }

    return lines.join('\n');
  }

  /**
   * Create display content with detailed information
   */
  createDisplayContent(pattern, files, summary) {
    const lines = [];
    
    // Header
    lines.push(`🔍 Glob Search: ${pattern}`);
    lines.push(`📁 Search path: ${summary.searchPath}`);
    lines.push(`📊 Found ${summary.totalFound} files, showing ${files.length}`);
    lines.push(`🔄 Sorted by ${summary.sortBy} (${summary.sortOrder})`);
    lines.push('');

    if (files.length === 0) {
      lines.push('❌ No files found matching the pattern.');
      return lines.join('\n');
    }

    // File listing with details
    const nameWidth = Math.max(20, Math.max(...files.map(f => f.relativePath.length)));
    const sizeWidth = 10;
    const dateWidth = 12;

    // Header row
    const header = [
      'File'.padEnd(nameWidth),
      'Size'.padEnd(sizeWidth),
      'Modified'.padEnd(dateWidth)
    ].join(' | ');
    
    lines.push(header);
    lines.push('-'.repeat(header.length));

    // File rows
    for (const file of files) {
      const row = [
        file.relativePath.padEnd(nameWidth),
        this.formatFileSize(file.size).padEnd(sizeWidth),
        file.modified.toISOString().split('T')[0].padEnd(dateWidth)
      ].join(' | ');
      
      lines.push(row);
    }

    // Summary
    if (summary.totalFound > files.length) {
      lines.push('');
      lines.push(`... and ${summary.totalFound - files.length} more files`);
    }

    return lines.join('\n');
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    // Large glob patterns might be expensive
    const riskyPatterns = [
      '**/*',
      '**/.*',
      '**/**/**/**/**'
    ];
    
    return riskyPatterns.some(pattern => params.pattern.includes(pattern));
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    if (this.isRiskyOperation(params)) {
      return `Execute potentially expensive glob pattern "${params.pattern}"? This might take a while and return many results.`;
    }
    return super.getConfirmationMessage(params);
  }

  /**
   * Optimize results with better filtering and prioritization
   */
  optimizeResults(files, options = {}) {
    const { maxResults, pattern, searchPath } = options;

    // If we have fewer files than max, return as-is
    if (files.length <= maxResults) {
      return files;
    }

    // Prioritize files based on various factors
    const prioritizedFiles = files.map(file => {
      let priority = 0;

      // Prioritize files closer to search root
      const depth = file.relativePath.split(path.sep).length;
      priority += Math.max(0, 10 - depth); // Prefer shallower files

      // Prioritize common file types
      const commonExtensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h'];
      if (commonExtensions.includes(file.extension.toLowerCase())) {
        priority += 5;
      }

      // Prioritize recently modified files
      const daysSinceModified = (Date.now() - file.modified.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceModified < 7) {
        priority += 3;
      } else if (daysSinceModified < 30) {
        priority += 1;
      }

      // Prioritize files with meaningful names (not temp, cache, etc.)
      const meaningfulPatterns = /\.(test|spec|config|index|main|app)\./i;
      const temporaryPatterns = /\.(tmp|temp|cache|log|bak)\./i;

      if (meaningfulPatterns.test(file.name)) {
        priority += 2;
      }
      if (temporaryPatterns.test(file.name)) {
        priority -= 3;
      }

      return { ...file, priority };
    });

    // Sort by priority (descending) then by original sort criteria
    prioritizedFiles.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      // Fall back to modification time if priorities are equal
      return b.modified.getTime() - a.modified.getTime();
    });

    // Remove priority field before returning
    return prioritizedFiles.map(({ priority, ...file }) => file);
  }
}
