import { BaseProvider } from './base-provider.js';

export class DeepSeekProvider extends BaseProvider {
  constructor(config) {
    super({
      ...config,
      supportsTools: true, // DeepSeek supports function calling
      functionCalling: {
        enabled: true,
        maxToolCalls: 8,
        maxRecursionDepth: 4,
        parallelExecution: true, // DeepSeek supports parallel function calls
        autoConfirm: config.functionCalling?.autoConfirm || false,
        safeMode: config.functionCalling?.safeMode !== false,
        timeout: config.functionCalling?.timeout || 35000
      }
    });
  }

  /**
   * Get authentication headers for DeepSeek
   */
  getAuthHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }



  /**
   * Format messages for DeepSeek API with tool support
   */
  formatMessages(messages) {
    return messages.map(msg => {
      const formatted = {
        role: msg.role,
        content: msg.content
      };

      // Handle tool calls in assistant messages (OpenAI-compatible format)
      if (msg.role === 'assistant' && msg.toolCalls) {
        formatted.tool_calls = msg.toolCalls.map(toolCall => ({
          id: toolCall.id,
          type: 'function',
          function: {
            name: toolCall.function.name,
            arguments: typeof toolCall.function.arguments === 'string'
              ? toolCall.function.arguments
              : JSON.stringify(toolCall.function.arguments)
          }
        }));
      }

      // Handle tool result messages
      if (msg.role === 'tool') {
        formatted.tool_call_id = msg.tool_call_id;
      }

      return formatted;
    });
  }

  /**
   * Parse DeepSeek API response with enhanced tool support
   */
  parseResponse(response) {
    if (!response.choices || response.choices.length === 0) {
      throw new Error('No response choices received from DeepSeek');
    }

    const choice = response.choices[0];
    const message = choice.message;

    const result = {
      content: message.content || '',
      role: message.role,
      finishReason: choice.finish_reason,
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0
      },
      model: response.model,
      provider: 'deepseek'
    };

    // Enhanced tool call handling (OpenAI-compatible format)
    if (message.tool_calls && message.tool_calls.length > 0) {
      result.toolCalls = message.tool_calls.map(toolCall => {
        let parsedArgs;
        try {
          parsedArgs = typeof toolCall.function.arguments === 'string'
            ? JSON.parse(toolCall.function.arguments || '{}')
            : toolCall.function.arguments || {};
        } catch (error) {
          logger.warn(`Failed to parse tool arguments for ${toolCall.function.name}:`, error.message);
          parsedArgs = {};
        }

        return {
          id: toolCall.id,
          type: toolCall.type || 'function',
          function: {
            name: toolCall.function.name,
            arguments: parsedArgs
          }
        };
      });
    }

    return result;
  }

  /**
   * Get additional parameters specific to DeepSeek
   */
  getAdditionalParams(options) {
    const params = {};
    
    if (options.systemPrompt) {
      // DeepSeek handles system prompts as part of messages like OpenAI
    }
    
    if (options.presencePenalty !== undefined) {
      params.presence_penalty = options.presencePenalty;
    }
    
    if (options.frequencyPenalty !== undefined) {
      params.frequency_penalty = options.frequencyPenalty;
    }
    
    if (options.topP !== undefined) {
      params.top_p = options.topP;
    }
    
    if (options.stop) {
      params.stop = options.stop;
    }
    
    return params;
  }

  /**
   * Format messages with system prompt support
   */
  formatMessages(messages, systemPrompt = null) {
    const formattedMessages = [];
    
    // Add system prompt if provided
    if (systemPrompt) {
      formattedMessages.push({
        role: 'system',
        content: systemPrompt
      });
    }
    
    // Add user messages
    messages.forEach(msg => {
      formattedMessages.push({
        role: msg.role,
        content: msg.content
      });
    });
    
    return formattedMessages;
  }

  /**
   * Send message with system prompt support
   */
  async sendMessage(messages, options = {}) {
    // Override formatMessages to include system prompt
    const originalFormatMessages = this.formatMessages;
    this.formatMessages = (msgs) => originalFormatMessages.call(this, msgs, options.systemPrompt);
    
    try {
      return await super.sendMessage(messages, options);
    } finally {
      // Restore original method
      this.formatMessages = originalFormatMessages;
    }
  }

  /**
   * Test connection with DeepSeek-specific handling
   */
  async testConnection() {
    try {
      const testMessage = [{ role: 'user', content: 'Hello' }];
      await this.sendMessage(testMessage, { maxTokens: 10 });
      return { success: true, message: `${this.name} connection successful` };
    } catch (error) {
      // Handle DeepSeek-specific errors
      if (error.response?.data?.error) {
        const errorData = error.response.data.error;
        throw new Error(`DeepSeek API error: ${errorData.message || errorData.type}`);
      }
      
      return { success: false, message: error.message };
    }
  }

  /**
   * Format tools for DeepSeek API request (OpenAI-compatible)
   */
  formatToolsForRequest(tools) {
    return tools.map(tool => {
      // Validate tool definition
      if (!tool.name || !tool.description) {
        logger.warn(`Invalid tool definition for DeepSeek:`, tool);
        return null;
      }

      return {
        type: 'function',
        function: {
          name: tool.name,
          description: tool.description,
          parameters: {
            type: 'object',
            properties: tool.parameters?.properties || {},
            required: tool.parameters?.required || [],
            additionalProperties: false
          }
        }
      };
    }).filter(Boolean); // Remove null entries
  }

  /**
   * Get DeepSeek-specific model capabilities
   */
  getModelCapabilities(model = this.defaultModel) {
    const capabilities = {
      supportsTools: true,
      maxToolCalls: 8,
      parallelToolCalls: true,
      toolChoiceOptions: ['auto', 'none']
    };

    // Model-specific capabilities
    if (model.includes('deepseek-chat')) {
      capabilities.maxToolCalls = 10;
      capabilities.complexReasoning = true;
    } else if (model.includes('deepseek-coder')) {
      capabilities.maxToolCalls = 12;
      capabilities.complexReasoning = true;
      capabilities.codeOptimized = true;
    }

    return capabilities;
  }

  /**
   * DeepSeek-specific optimizations
   */
  optimizeForDeepSeek(toolCalls, model = this.defaultModel) {
    const capabilities = this.getModelCapabilities(model);

    // Limit tool calls based on model capabilities
    if (toolCalls.length > capabilities.maxToolCalls) {
      logger.warn(`Limiting tool calls from ${toolCalls.length} to ${capabilities.maxToolCalls} for DeepSeek model ${model}`);
      return toolCalls.slice(0, capabilities.maxToolCalls);
    }

    // For coder models, prioritize code-related tools
    if (capabilities.codeOptimized) {
      const codeTools = ['write_file', 'read_file', 'replace', 'run_shell_command', 'glob', 'search_file_content'];
      const prioritized = toolCalls.sort((a, b) => {
        const aIsCode = codeTools.includes(a.function.name);
        const bIsCode = codeTools.includes(b.function.name);
        if (aIsCode && !bIsCode) return -1;
        if (!aIsCode && bIsCode) return 1;
        return 0;
      });
      return prioritized;
    }

    return toolCalls;
  }

  /**
   * Enhanced error handling for DeepSeek
   */
  handleDeepSeekError(error) {
    if (error.response?.data?.error) {
      const errorData = error.response.data.error;

      // DeepSeek-specific error codes
      if (errorData.code === 'insufficient_quota') {
        throw new Error('DeepSeek quota exceeded. Please check your account balance.');
      }

      if (errorData.code === 'model_not_found') {
        throw new Error(`DeepSeek model not found: ${errorData.message}`);
      }

      if (errorData.code === 'invalid_request_error') {
        throw new Error(`DeepSeek request error: ${errorData.message}`);
      }
    }

    // Fall back to base error handling
    throw error;
  }
}
