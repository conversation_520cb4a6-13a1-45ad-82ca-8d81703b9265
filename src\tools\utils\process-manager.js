import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import os from 'os';
import { SafeCommands, ToolDefaults } from '../base/tool-types.js';
import logger from '../../utils/logger.js';

const execAsync = promisify(exec);

/**
 * Process management utilities for shell command execution
 */
class ProcessManager {
  constructor() {
    this.activeProcesses = new Map();
    this.processGroups = new Map();
    this.backgroundPids = new Set();
    this.processCounter = 0;
  }

  /**
   * Execute shell command with sandboxing
   */
  async executeCommand(command, options = {}) {
    const {
      cwd = process.cwd(),
      timeout = ToolDefaults.timeout,
      env = process.env,
      shell = true,
      maxBuffer = 1024 * 1024, // 1MB
      encoding = 'utf8'
    } = options;

    const processId = ++this.processCounter;
    
    try {
      logger.debug(`Executing command [${processId}]: ${command}`);
      
      // Validate command safety
      this.validateCommand(command);
      
      const execOptions = {
        cwd: path.resolve(cwd),
        timeout,
        env: { ...env },
        maxBuffer,
        encoding,
        shell
      };

      const startTime = Date.now();
      const result = await execAsync(command, execOptions);
      const executionTime = Date.now() - startTime;

      logger.debug(`Command [${processId}] completed in ${executionTime}ms`);

      return {
        stdout: result.stdout,
        stderr: result.stderr,
        exitCode: 0,
        executionTime,
        processId,
        command
      };

    } catch (error) {
      const executionTime = Date.now() - (error.startTime || Date.now());
      
      logger.error(`Command [${processId}] failed:`, error.message);

      return {
        stdout: error.stdout || '',
        stderr: error.stderr || error.message,
        exitCode: error.code || 1,
        executionTime,
        processId,
        command,
        error: error.message
      };
    }
  }

  /**
   * Execute command with streaming output
   */
  async executeCommandStreaming(command, options = {}) {
    const {
      cwd = process.cwd(),
      timeout = ToolDefaults.timeout,
      env = process.env,
      onStdout = null,
      onStderr = null,
      onProgress = null
    } = options;

    const processId = ++this.processCounter;
    
    return new Promise((resolve, reject) => {
      logger.debug(`Executing streaming command [${processId}]: ${command}`);
      
      try {
        this.validateCommand(command);
      } catch (error) {
        reject(error);
        return;
      }

      const startTime = Date.now();
      let stdout = '';
      let stderr = '';

      // Determine shell based on platform
      const shell = os.platform() === 'win32' ? 'cmd.exe' : '/bin/bash';
      const shellArgs = os.platform() === 'win32' ? ['/c'] : ['-c'];

      const childProcess = spawn(shell, [...shellArgs, command], {
        cwd: path.resolve(cwd),
        env: { ...env },
        stdio: ['pipe', 'pipe', 'pipe'],
        detached: true
      });

      this.activeProcesses.set(processId, childProcess);

      // Track process group
      if (childProcess.pid) {
        this.processGroups.set(processId, childProcess.pid);
        this.trackBackgroundProcesses(childProcess.pid);
      }

      // Set up timeout
      const timeoutId = setTimeout(() => {
        this.killProcessGroup(processId);
        reject(new Error(`Command timed out after ${timeout}ms`));
      }, timeout);

      // Handle stdout
      childProcess.stdout.on('data', (data) => {
        const chunk = data.toString();
        stdout += chunk;
        
        if (onStdout) {
          onStdout(chunk);
        }
        
        if (onProgress) {
          onProgress({ type: 'stdout', data: chunk });
        }
      });

      // Handle stderr
      childProcess.stderr.on('data', (data) => {
        const chunk = data.toString();
        stderr += chunk;
        
        if (onStderr) {
          onStderr(chunk);
        }
        
        if (onProgress) {
          onProgress({ type: 'stderr', data: chunk });
        }
      });

      // Handle process completion
      childProcess.on('close', (exitCode) => {
        clearTimeout(timeoutId);
        this.activeProcesses.delete(processId);
        this.processGroups.delete(processId);

        const executionTime = Date.now() - startTime;

        logger.debug(`Streaming command [${processId}] completed with exit code ${exitCode}`);

        resolve({
          stdout,
          stderr,
          exitCode,
          executionTime,
          processId,
          command
        });
      });

      // Handle process errors
      childProcess.on('error', (error) => {
        clearTimeout(timeoutId);
        this.activeProcesses.delete(processId);
        this.processGroups.delete(processId);

        logger.error(`Streaming command [${processId}] error:`, error.message);
        reject(error);
      });
    });
  }

  /**
   * Validate command safety
   */
  validateCommand(command) {
    if (!command || typeof command !== 'string') {
      throw new Error('Command must be a non-empty string');
    }

    // Extract root command
    const rootCommand = this.extractRootCommand(command);
    
    // Check against whitelist
    if (!this.isCommandSafe(rootCommand)) {
      throw new Error(`Command not allowed: ${rootCommand}. Use only whitelisted commands.`);
    }

    // Check for dangerous patterns
    this.checkDangerousPatterns(command);
  }

  /**
   * Extract root command from command string
   */
  extractRootCommand(command) {
    // Remove leading/trailing whitespace
    const trimmed = command.trim();
    
    // Handle command chains and pipes
    const parts = trimmed.split(/[;&|]/).map(part => part.trim());
    const firstPart = parts[0];
    
    // Extract the actual command (first word)
    const words = firstPart.split(/\s+/);
    return words[0];
  }

  /**
   * Check if command is in safe list
   */
  isCommandSafe(command) {
    return SafeCommands.includes(command) || 
           SafeCommands.some(safe => command.startsWith(safe));
  }

  /**
   * Check for dangerous command patterns
   */
  checkDangerousPatterns(command) {
    const dangerousPatterns = [
      /rm\s+-rf\s+\//, // rm -rf /
      />\s*\/dev\//, // redirect to device files
      /sudo\s+/, // sudo commands
      /su\s+/, // switch user
      /chmod\s+777/, // dangerous permissions
      /curl.*\|\s*sh/, // pipe to shell
      /wget.*\|\s*sh/, // pipe to shell
      /eval\s*\(/, // eval execution
      /exec\s*\(/, // exec execution
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(command)) {
        throw new Error(`Dangerous command pattern detected: ${pattern.source}`);
      }
    }
  }

  /**
   * Track background processes spawned by a command
   */
  async trackBackgroundProcesses(parentPid) {
    try {
      if (os.platform() === 'win32') {
        // Use wmic to find child processes on Windows
        const result = await this.executeCommand(`wmic process where "ParentProcessId=${parentPid}" get ProcessId /format:csv`);
        const lines = result.stdout.split('\n').filter(line => line.includes(','));
        for (const line of lines) {
          const parts = line.split(',');
          if (parts.length > 1 && parts[1].trim()) {
            this.backgroundPids.add(parseInt(parts[1].trim()));
          }
        }
      } else {
        // Use pgrep to find child processes on Unix-like systems
        const result = await this.executeCommand(`pgrep -P ${parentPid}`);
        const pids = result.stdout.split('\n').filter(pid => pid.trim());
        for (const pid of pids) {
          this.backgroundPids.add(parseInt(pid.trim()));
        }
      }
    } catch (error) {
      logger.debug(`Failed to track background processes for ${parentPid}:`, error.message);
    }
  }

  /**
   * Kill a running process and its process group
   */
  killProcessGroup(processId) {
    const process = this.activeProcesses.get(processId);
    const groupPid = this.processGroups.get(processId);

    if (process) {
      try {
        // Kill process group to ensure all child processes are terminated
        if (process.pid) {
          if (os.platform() === 'win32') {
            spawn('taskkill', ['/pid', process.pid, '/t', '/f']);
          } else {
            // Kill the entire process group
            try {
              process.kill('SIGTERM');
              // Also try to kill the process group
              if (groupPid) {
                process.kill(-groupPid, 'SIGTERM');
              }
            } catch (killError) {
              logger.debug(`SIGTERM failed, trying SIGKILL:`, killError.message);
            }

            // Force kill after 5 seconds if still running
            setTimeout(() => {
              if (!process.killed) {
                try {
                  process.kill('SIGKILL');
                  if (groupPid) {
                    process.kill(-groupPid, 'SIGKILL');
                  }
                } catch (killError) {
                  logger.debug(`SIGKILL failed:`, killError.message);
                }
              }
            }, 5000);
          }
        }

        this.activeProcesses.delete(processId);
        this.processGroups.delete(processId);
        logger.debug(`Process group [${processId}] killed`);

      } catch (error) {
        logger.error(`Failed to kill process group [${processId}]:`, error.message);
      }
    }
  }

  /**
   * Kill a running process (legacy method, now calls killProcessGroup)
   */
  killProcess(processId) {
    return this.killProcessGroup(processId);
  }

  /**
   * Kill all active processes and background processes
   */
  killAllProcesses() {
    const processIds = Array.from(this.activeProcesses.keys());
    for (const processId of processIds) {
      this.killProcessGroup(processId);
    }

    // Also kill tracked background processes
    for (const pid of this.backgroundPids) {
      try {
        if (os.platform() === 'win32') {
          spawn('taskkill', ['/pid', pid, '/f']);
        } else {
          process.kill(pid, 'SIGTERM');
        }
      } catch (error) {
        logger.debug(`Failed to kill background process ${pid}:`, error.message);
      }
    }

    this.backgroundPids.clear();
    logger.debug(`Killed ${processIds.length} active processes and background processes`);
  }

  /**
   * Get active process information
   */
  getActiveProcesses() {
    const processes = [];
    for (const [processId, process] of this.activeProcesses) {
      processes.push({
        processId,
        pid: process.pid,
        killed: process.killed,
        exitCode: process.exitCode
      });
    }
    return processes;
  }

  /**
   * Check if git is available and we're in a git repository
   */
  async isGitRepository(cwd = process.cwd()) {
    try {
      const result = await this.executeCommand('git rev-parse --git-dir', { cwd });
      return result.exitCode === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get system information
   */
  getSystemInfo() {
    return {
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      shell: os.platform() === 'win32' ? 'cmd.exe' : process.env.SHELL || '/bin/bash',
      cwd: process.cwd(),
      env: {
        PATH: process.env.PATH,
        HOME: process.env.HOME || process.env.USERPROFILE,
        USER: process.env.USER || process.env.USERNAME
      }
    };
  }
}

export default new ProcessManager();
