import fs from 'fs-extra';
import path from 'path';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import fileService from '../utils/file-service.js';
import diffGenerator from '../utils/diff-generator.js';
import logger from '../../utils/logger.js';

/**
 * WriteFileTool - Creates new files or overwrites existing ones
 */
export class WriteFileTool extends BaseTool {
  constructor() {
    super({
      name: 'write_file',
      description: 'Creates new files or overwrites existing ones with diff preview',
      category: ToolCategory.FILESYSTEM,
      permissionLevel: PermissionLevel.MODERATE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          file_path: {
            type: 'string',
            description: 'Absolute path for the file to write'
          },
          content: {
            type: 'string',
            description: 'Content to write to the file'
          },
          modified_by_user: {
            type: 'boolean',
            description: 'Flag indicating if content was modified by user',
            default: false
          },
          create_backup: {
            type: 'boolean',
            description: 'Whether to create a backup of existing file',
            default: true
          }
        },
        required: ['file_path', 'content']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate file_path
    if (!params.file_path) {
      errors.push('file_path is required');
    } else if (typeof params.file_path !== 'string') {
      errors.push('file_path must be a string');
    }

    // Validate content
    if (params.content === undefined || params.content === null) {
      errors.push('content is required');
    } else if (typeof params.content !== 'string') {
      errors.push('content must be a string');
    }

    // Validate boolean parameters
    if (params.modified_by_user !== undefined && typeof params.modified_by_user !== 'boolean') {
      errors.push('modified_by_user must be a boolean');
    }

    if (params.create_backup !== undefined && typeof params.create_backup !== 'boolean') {
      errors.push('create_backup must be a boolean');
    }

    // Validate path
    if (params.file_path) {
      try {
        fileService.validatePath(params.file_path);
      } catch (error) {
        errors.push(`Invalid path: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    try {
      const resolvedPath = fileService.validatePath(params.file_path);
      
      // Always risky if file exists (overwriting)
      if (fileService.exists(resolvedPath)) {
        return true;
      }

      // Check if writing to important directories
      const importantPaths = [
        'package.json',
        'package-lock.json',
        '.gitignore',
        'README.md',
        'LICENSE'
      ];

      const fileName = path.basename(params.file_path);
      return importantPaths.includes(fileName);

    } catch (error) {
      return true; // Assume risky if we can't validate
    }
  }

  /**
   * AI-powered content correction
   */
  async ensureCorrectContent(content, context = {}) {
    const { filePath, fileExtension } = context;

    // For now, return content as-is
    // In a full implementation, this would use an AI service to:
    // - Fix syntax errors
    // - Ensure proper formatting
    // - Validate content structure
    // - Apply best practices

    return content;
  }

  /**
   * Generate preview for confirmation
   */
  async generatePreview(params) {
    const { file_path: filePath, content, modified_by_user = false } = params;

    try {
      const resolvedPath = fileService.validatePath(filePath);
      const relativePath = path.relative(fileService.rootDirectory, resolvedPath);
      const fileExists = fileService.exists(resolvedPath);

      let preview = '';
      let metadata = {
        operation: fileExists ? 'overwrite' : 'create',
        filePath: relativePath,
        contentLength: content.length,
        fileExists
      };

      if (fileExists) {
        // Show diff for existing files
        const existingContent = fs.readFileSync(resolvedPath, 'utf8');
        const diffResult = diffGenerator.createChangePreview(existingContent, content, relativePath);

        preview = diffResult.preview;
        metadata.diff = diffResult.diff;
        metadata.stats = diffResult.stats;
      } else {
        // Show content preview for new files
        const lines = content.split('\n');
        const previewLines = lines.slice(0, 20); // Show first 20 lines

        preview = [
          `Creating new file: ${relativePath}`,
          `Content length: ${content.length} characters, ${lines.length} lines`,
          '',
          'Content preview:',
          '─'.repeat(50),
          ...previewLines.map((line, i) => `${(i + 1).toString().padStart(3)}: ${line}`),
          lines.length > 20 ? `... (${lines.length - 20} more lines)` : '',
          '─'.repeat(50)
        ].filter(Boolean).join('\n');
      }

      return {
        hasPreview: true,
        preview,
        metadata
      };

    } catch (error) {
      return {
        hasPreview: false,
        preview: `Error generating preview: ${error.message}`,
        metadata: { error: error.message }
      };
    }
  }

  /**
   * Execute file writing
   */
  async execute(params) {
    const {
      file_path: filePath,
      content,
      modified_by_user = false,
      create_backup = true
    } = params;

    try {
      const resolvedPath = fileService.validatePath(filePath);
      const relativePath = path.relative(fileService.rootDirectory, resolvedPath);

      logger.debug(`Writing file: ${relativePath}`);

      // Apply AI content correction if not modified by user
      let finalContent = content;
      if (!modified_by_user) {
        const fileExtension = path.extname(filePath);
        finalContent = await this.ensureCorrectContent(content, {
          filePath: relativePath,
          fileExtension
        });
      }

      // Check if file exists
      const fileExists = fileService.exists(resolvedPath);
      let existingContent = '';
      let backupPath = null;

      if (fileExists) {
        existingContent = fs.readFileSync(resolvedPath, 'utf8');

        // Create backup if requested
        if (create_backup) {
          backupPath = `${resolvedPath}.backup.${Date.now()}`;
          fs.copyFileSync(resolvedPath, backupPath);
          logger.debug(`Created backup: ${backupPath}`);
        }
      }

      // Ensure parent directory exists
      const parentDir = path.dirname(resolvedPath);
      fileService.ensureDir(parentDir);

      // Write content
      fs.writeFileSync(resolvedPath, finalContent, 'utf8');

      // Generate diff for display
      const diff = fileExists 
        ? diffGenerator.createChangePreview(existingContent, content, relativePath)
        : null;

      // Create result content
      const resultContent = this.formatWriteResult(
        relativePath,
        fileExists,
        content.length,
        diff
      );

      // Create display content
      const displayContent = this.createDisplayContent(
        relativePath,
        fileExists,
        content.length,
        diff,
        backupPath
      );

      const metadata = {
        filePath: relativePath,
        absolutePath: resolvedPath,
        operation: fileExists ? 'overwrite' : 'create',
        contentLength: content.length,
        backupPath,
        diff: diff ? diff.stats : null
      };

      logger.info(`${fileExists ? 'Updated' : 'Created'} file: ${relativePath}`);

      return ToolResult.success(resultContent, {
        displayContent,
        metadata
      });

    } catch (error) {
      logger.error(`Failed to write file ${filePath}:`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Format write result for LLM
   */
  formatWriteResult(relativePath, fileExists, contentLength, diff) {
    const lines = [];
    
    lines.push(`${fileExists ? 'Updated' : 'Created'} file: ${relativePath}`);
    lines.push(`Content length: ${contentLength} characters`);
    
    if (diff && diff.hasChanges) {
      lines.push('');
      lines.push('Changes:');
      lines.push(`  +${diff.stats.additions} additions`);
      lines.push(`  -${diff.stats.deletions} deletions`);
    }

    return lines.join('\n');
  }

  /**
   * Create display content with diff preview
   */
  createDisplayContent(relativePath, fileExists, contentLength, diff, backupPath) {
    const lines = [];
    
    // Header
    const operation = fileExists ? '📝 Updated' : '📄 Created';
    lines.push(`${operation} file: ${relativePath}`);
    lines.push(`Content length: ${contentLength} characters`);
    
    if (backupPath) {
      lines.push(`Backup created: ${path.basename(backupPath)}`);
    }
    
    // Show diff for existing files
    if (diff && diff.hasChanges) {
      lines.push('');
      lines.push('📊 Changes Summary:');
      lines.push(`  +${diff.stats.additions} additions`);
      lines.push(`  -${diff.stats.deletions} deletions`);
      lines.push(`  ${diff.stats.unchanged} unchanged lines`);
      
      lines.push('');
      lines.push('🔍 Diff Preview:');
      lines.push(diff.preview);
    }

    return lines.join('\n');
  }

  /**
   * Ensure correct content formatting using AI
   */
  async ensureCorrectContent(content, filePath) {
    // This is a placeholder for AI-powered content correction
    // In a real implementation, this would use an AI service to ensure
    // the content is properly formatted for the file type
    
    const ext = path.extname(filePath).toLowerCase();
    
    // Basic formatting based on file extension
    switch (ext) {
      case '.json':
        try {
          const parsed = JSON.parse(content);
          return JSON.stringify(parsed, null, 2);
        } catch (error) {
          // Return original content if not valid JSON
          return content;
        }
      
      case '.js':
      case '.ts':
        // Ensure proper line endings
        return content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      
      default:
        return content;
    }
  }
}
