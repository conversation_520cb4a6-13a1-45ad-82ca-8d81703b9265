import path from 'path';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import fileService from '../utils/file-service.js';
import contentProcessor from '../utils/content-processor.js';
import logger from '../../utils/logger.js';

/**
 * ReadFileTool - Reads content from individual files
 */
export class ReadFileTool extends BaseTool {
  constructor() {
    super({
      name: 'read_file',
      description: 'Reads content from individual files with support for text, images, and PDFs',
      category: ToolCategory.FILESYSTEM,
      permissionLevel: PermissionLevel.SAFE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          absolute_path: {
            type: 'string',
            description: 'Absolute path to the file to read'
          },
          offset: {
            type: 'integer',
            description: 'Starting line number for pagination (0-based)',
            minimum: 0,
            default: 0
          },
          limit: {
            type: 'integer',
            description: 'Number of lines to read (null for all)',
            minimum: 1,
            default: null
          }
        },
        required: ['absolute_path']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate absolute_path
    if (!params.absolute_path) {
      errors.push('absolute_path is required');
    } else if (typeof params.absolute_path !== 'string') {
      errors.push('absolute_path must be a string');
    }

    // Validate offset
    if (params.offset !== undefined) {
      if (!Number.isInteger(params.offset) || params.offset < 0) {
        errors.push('offset must be a non-negative integer');
      }
    }

    // Validate limit
    if (params.limit !== undefined && params.limit !== null) {
      if (!Number.isInteger(params.limit) || params.limit < 1) {
        errors.push('limit must be a positive integer');
      }
    }

    // Check if file exists and is accessible
    if (params.absolute_path) {
      try {
        const resolvedPath = fileService.validatePath(params.absolute_path);
        
        if (!fileService.exists(resolvedPath)) {
          errors.push(`File does not exist: ${params.absolute_path}`);
        } else {
          const stats = fileService.getStats(resolvedPath);
          if (stats.isDirectory()) {
            errors.push(`Path is a directory, not a file: ${params.absolute_path}`);
          }
        }

        // Check if file should be ignored
        if (fileService.shouldIgnore(resolvedPath)) {
          errors.push(`File is in ignore list: ${params.absolute_path}`);
        }

      } catch (error) {
        errors.push(`Invalid path: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Execute file reading
   */
  async execute(params) {
    const {
      absolute_path: filePath,
      offset = 0,
      limit = null
    } = params;

    try {
      logger.debug(`Reading file: ${filePath}`);

      // Process file content based on type
      const result = await contentProcessor.processSingleFileContent(filePath, {
        offset,
        limit
      });

      // Get file information
      const stats = fileService.getStats(filePath);
      const fileType = fileService.getFileType(filePath);
      const relativePath = path.relative(fileService.rootDirectory, filePath);

      // Create formatted content for LLM
      const content = this.formatFileContent(result, relativePath, fileType);

      // Create display content
      const displayContent = this.createDisplayContent(result, relativePath, fileType, stats);

      // Enhanced result with telemetry
      const toolResult = ToolResult.success(content, {
        displayContent,
        metadata: {
          filePath: relativePath,
          absolutePath: filePath,
          fileType,
          size: stats.size,
          modified: stats.mtime,
          created: stats.birthtime,
          contentMetadata: result.metadata,
          pagination: {
            offset,
            limit,
            hasMore: limit && result.metadata.totalLines > offset + limit
          },
          processingTime: result.processingTime || 0,
          contentLength: result.content ? result.content.length : 0
        }
      });

      // Add telemetry tags
      toolResult.addTag('file-read');
      toolResult.addTag(`file-type:${fileType}`);

      if (result.metadata) {
        if (result.metadata.hasText !== undefined) {
          toolResult.addTag(result.metadata.hasText ? 'has-text' : 'no-text');
        }
        if (result.metadata.pages) {
          toolResult.addTag(`pages:${result.metadata.pages}`);
        }
      }

      // Add context information
      toolResult.addContext('fileSize', stats.size);
      toolResult.addContext('fileExtension', path.extname(filePath));

      if (offset > 0 || limit) {
        toolResult.addContext('pagination', { offset, limit });
      }

      return toolResult;

    } catch (error) {
      logger.error(`Failed to read file ${filePath}:`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Format file content for LLM consumption
   */
  formatFileContent(result, relativePath, fileType) {
    const lines = [];
    
    lines.push(`File: ${relativePath}`);
    lines.push(`Type: ${fileType}`);
    
    if (result.metadata.totalLines) {
      lines.push(`Lines: ${result.metadata.processedLines}/${result.metadata.totalLines}`);
    }
    
    if (result.metadata.size) {
      lines.push(`Size: ${this.formatFileSize(result.metadata.size)}`);
    }
    
    lines.push(''); // Empty line before content
    lines.push('Content:');
    lines.push('```');
    lines.push(result.content);
    lines.push('```');

    return lines.join('\n');
  }

  /**
   * Create display content with additional formatting
   */
  createDisplayContent(result, relativePath, fileType, stats) {
    const lines = [];
    
    // File header
    lines.push(`📄 ${relativePath}`);
    lines.push(`   Type: ${fileType}`);
    lines.push(`   Size: ${this.formatFileSize(stats.size)}`);
    lines.push(`   Modified: ${stats.mtime.toISOString()}`);
    
    // Content metadata
    if (result.metadata.totalLines) {
      const { startLine, endLine, totalLines } = result.metadata;
      lines.push(`   Lines: ${startLine}-${endLine} of ${totalLines}`);
    }
    
    if (result.metadata.encoding) {
      lines.push(`   Encoding: ${result.metadata.encoding}`);
    }
    
    lines.push(''); // Empty line
    
    // Content with line numbers for text files
    if (fileType === 'text' && result.metadata.totalLines) {
      const contentLines = result.content.split('\n');
      const startLineNum = result.metadata.startLine || 1;
      
      for (let i = 0; i < contentLines.length; i++) {
        const lineNum = (startLineNum + i).toString().padStart(4);
        lines.push(`${lineNum} | ${contentLines[i]}`);
      }
    } else {
      lines.push(result.content);
    }

    // Additional metadata for special file types
    if (fileType === 'image' && result.metadata.width) {
      lines.push('');
      lines.push(`Image dimensions: ${result.metadata.width}x${result.metadata.height}`);
      lines.push(`Format: ${result.metadata.format}`);
    }
    
    if (fileType === 'pdf' && result.metadata.pages) {
      lines.push('');
      lines.push(`PDF pages: ${result.metadata.pages}`);
      if (result.metadata.info && result.metadata.info.Title) {
        lines.push(`Title: ${result.metadata.info.Title}`);
      }
    }

    return lines.join('\n');
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    try {
      const stats = fileService.getStats(params.absolute_path);
      // Consider large files risky
      return stats.size > 5 * 1024 * 1024; // 5MB
    } catch (error) {
      return false;
    }
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    try {
      const stats = fileService.getStats(params.absolute_path);
      const size = this.formatFileSize(stats.size);
      return `Read file ${params.absolute_path} (${size})?`;
    } catch (error) {
      return super.getConfirmationMessage(params);
    }
  }
}
