import { BaseProvider } from './base-provider.js';

export class AnthropicProvider extends BaseProvider {
  constructor(config) {
    super({
      ...config,
      supportsTools: true, // Anthropic supports function calling
      functionCalling: {
        enabled: true,
        maxToolCalls: 5, // Anthropic typically handles fewer parallel calls
        maxRecursionDepth: 3,
        parallelExecution: false, // Anthropic processes tools sequentially
        autoConfirm: config.functionCalling?.autoConfirm || false,
        safeMode: config.functionCalling?.safeMode !== false,
        timeout: config.functionCalling?.timeout || 45000 // Anthropic can be slower
      }
    });
  }

  /**
   * Get authentication headers for Anthropic
   */
  getAuthHeaders() {
    return {
      'x-api-key': this.apiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    };
  }

  /**
   * Format messages for Anthropic API with enhanced tool support
   */
  formatMessages(messages) {
    // Anthropic expects alternating user/assistant messages
    // Filter out system messages as they're handled separately
    return messages
      .filter(msg => msg.role !== 'system')
      .map(msg => {
        const formatted = {
          role: msg.role === 'assistant' ? 'assistant' : 'user',
          content: []
        };

        // Handle text content
        if (msg.content) {
          formatted.content.push({
            type: 'text',
            text: msg.content
          });
        }

        // Handle tool calls in assistant messages
        if (msg.role === 'assistant' && msg.toolCalls) {
          msg.toolCalls.forEach(toolCall => {
            formatted.content.push({
              type: 'tool_use',
              id: toolCall.id,
              name: toolCall.function.name,
              input: typeof toolCall.function.arguments === 'string'
                ? JSON.parse(toolCall.function.arguments)
                : toolCall.function.arguments
            });
          });
        }

        // Handle tool result messages
        if (msg.role === 'tool') {
          formatted.role = 'user';
          formatted.content = [{
            type: 'tool_result',
            tool_use_id: msg.tool_call_id,
            content: msg.content
          }];
        }

        return formatted;
      });
  }

  /**
   * Parse Anthropic API response
   */
  parseResponse(response) {
    if (!response.content || response.content.length === 0) {
      throw new Error('No content received from Anthropic');
    }

    const textContent = response.content.find(block => block.type === 'text');

    const result = {
      content: textContent?.text || '',
      role: 'assistant',
      finishReason: response.stop_reason,
      usage: {
        promptTokens: response.usage?.input_tokens || 0,
        completionTokens: response.usage?.output_tokens || 0,
        totalTokens: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0)
      },
      model: response.model,
      provider: 'anthropic'
    };

    // Enhanced tool call handling for Anthropic format
    const toolUseBlocks = response.content?.filter(block => block.type === 'tool_use') || [];
    if (toolUseBlocks.length > 0) {
      result.toolCalls = toolUseBlocks.map(block => {
        // Validate tool use block
        if (!block.name || !block.id) {
          logger.warn(`Invalid tool use block from Anthropic:`, block);
          return null;
        }

        return {
          id: block.id,
          type: 'function',
          function: {
            name: block.name,
            arguments: block.input || {}
          }
        };
      }).filter(Boolean); // Remove null entries
    }

    return result;
  }

  /**
   * Get additional parameters specific to Anthropic
   */
  getAdditionalParams(options) {
    const params = {};
    
    if (options.systemPrompt) {
      params.system = options.systemPrompt;
    }
    
    if (options.topP !== undefined) {
      params.top_p = options.topP;
    }
    
    if (options.topK !== undefined) {
      params.top_k = options.topK;
    }
    
    if (options.stop) {
      params.stop_sequences = Array.isArray(options.stop) ? options.stop : [options.stop];
    }
    
    return params;
  }

  /**
   * Format tools for Anthropic API request
   */
  formatToolsForRequest(tools) {
    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      input_schema: tool.parameters
    }));
  }

  /**
   * Parse tool calls from Anthropic response
   */
  parseToolCallsFromResponse(response) {
    if (!response.content) {
      return [];
    }

    const toolUseBlocks = response.content.filter(block => block.type === 'tool_use');
    return toolUseBlocks.map(block => ({
      id: block.id,
      type: 'function',
      function: {
        name: block.name,
        arguments: block.input || {}
      }
    }));
  }

  /**
   * Override sendMessage to handle Anthropic's different API structure
   */
  async sendMessage(messages, options = {}) {
    try {
      this.validateConfig();
      
      const client = this.createHttpClient();
      const formattedMessages = this.formatMessages(messages);
      
      const requestData = {
        model: options.model || this.defaultModel,
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        messages: formattedMessages,
        ...this.getAdditionalParams(options)
      };

      // Add tools if supported and enabled
      if (this.supportsToolCalling() && options.enableTools !== false) {
        const tools = this.getAvailableTools();
        if (tools.length > 0) {
          requestData.tools = this.formatToolsForRequest(tools);
        }
      }

      // Remove stream parameter as Anthropic handles it differently
      delete requestData.stream;

      const response = await client.post('/messages', requestData);
      
      return this.parseResponse(response.data);
      
    } catch (error) {
      // Handle Anthropic-specific errors
      if (error.response?.data?.error) {
        const errorData = error.response.data.error;
        throw new Error(`Anthropic API error: ${errorData.message || errorData.type}`);
      }
      
      throw error;
    }
  }

  /**
   * Test connection with Anthropic-specific endpoint
   */
  async testConnection() {
    try {
      const testMessage = [{ role: 'user', content: 'Hello' }];
      await this.sendMessage(testMessage, { maxTokens: 10 });
      return { success: true, message: `${this.name} connection successful` };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * Format tools for Anthropic API request with enhanced validation
   */
  formatToolsForRequest(tools) {
    return tools.map(tool => {
      // Validate tool definition
      if (!tool.name || !tool.description) {
        logger.warn(`Invalid tool definition for Anthropic:`, tool);
        return null;
      }

      return {
        name: tool.name,
        description: tool.description,
        input_schema: {
          type: 'object',
          properties: tool.parameters?.properties || {},
          required: tool.parameters?.required || []
        }
      };
    }).filter(Boolean); // Remove null entries
  }

  /**
   * Get Anthropic-specific model capabilities
   */
  getModelCapabilities(model = this.defaultModel) {
    const capabilities = {
      supportsTools: true,
      maxToolCalls: 5, // Anthropic typically handles fewer parallel calls
      parallelToolCalls: false, // Sequential processing
      toolChoiceOptions: ['auto', 'any', 'tool']
    };

    // Model-specific capabilities
    if (model.includes('claude-3')) {
      capabilities.maxToolCalls = 8;
      capabilities.complexReasoning = true;
      capabilities.multimodalSupport = true;
    } else if (model.includes('claude-2')) {
      capabilities.maxToolCalls = 3;
      capabilities.complexReasoning = false;
      capabilities.multimodalSupport = false;
    }

    return capabilities;
  }

  /**
   * Anthropic-specific tool choice handling
   */
  formatToolChoice(toolChoice) {
    if (!toolChoice || toolChoice === 'auto') {
      return { type: 'auto' };
    }

    if (toolChoice === 'any') {
      return { type: 'any' };
    }

    if (typeof toolChoice === 'string') {
      return { type: 'tool', name: toolChoice };
    }

    return { type: 'auto' };
  }
}
