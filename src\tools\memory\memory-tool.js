import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import logger from '../../utils/logger.js';

/**
 * MemoryTool - Saves information to persistent AI memory
 */
export class MemoryTool extends BaseTool {
  constructor() {
    super({
      name: 'save_memory',
      description: 'Saves information to persistent AI memory for future reference',
      category: ToolCategory.MEMORY,
      permissionLevel: PermissionLevel.SAFE
    });
    
    // Use ~/.gemini/GEMINI.md as specified in the architecture
    this.memoryDir = path.join(os.homedir(), '.gemini');
    this.memoryFile = path.join(this.memoryDir, 'GEMINI.md');
    this.maxMemorySize = 1024 * 1024; // 1MB
    this.maxFactLength = 1000; // 1000 characters per fact
    this.memorySectionHeader = '## Gemini Added Memories';
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          fact: {
            type: 'string',
            description: 'Information to remember (should be concise and factual)'
          },
          category: {
            type: 'string',
            description: 'Category for organizing the memory',
            default: 'General'
          },
          importance: {
            type: 'string',
            description: 'Importance level of the information',
            enum: ['low', 'medium', 'high'],
            default: 'medium'
          },
          tags: {
            type: 'array',
            items: { type: 'string' },
            description: 'Tags for categorizing and searching the memory',
            default: []
          }
        },
        required: ['fact']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate fact
    if (!params.fact) {
      errors.push('fact is required');
    } else if (typeof params.fact !== 'string') {
      errors.push('fact must be a string');
    } else if (params.fact.trim() === '') {
      errors.push('fact cannot be empty');
    } else if (params.fact.length > this.maxFactLength) {
      errors.push(`fact must be ${this.maxFactLength} characters or less`);
    }

    // Validate category
    if (params.category !== undefined) {
      if (typeof params.category !== 'string') {
        errors.push('category must be a string');
      } else if (params.category.length > 50) {
        errors.push('category must be 50 characters or less');
      }
    }

    // Validate importance
    if (params.importance !== undefined) {
      const validImportance = ['low', 'medium', 'high'];
      if (!validImportance.includes(params.importance)) {
        errors.push(`importance must be one of: ${validImportance.join(', ')}`);
      }
    }

    // Validate tags
    if (params.tags !== undefined) {
      if (!Array.isArray(params.tags)) {
        errors.push('tags must be an array');
      } else {
        for (const tag of params.tags) {
          if (typeof tag !== 'string') {
            errors.push('all tags must be strings');
            break;
          } else if (tag.length > 20) {
            errors.push('tags must be 20 characters or less');
            break;
          }
        }
        
        if (params.tags.length > 10) {
          errors.push('maximum 10 tags allowed');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Execute memory saving
   */
  async execute(params) {
    const {
      fact,
      category = 'General',
      importance = 'medium',
      tags = []
    } = params;

    try {
      logger.debug(`Saving memory: ${fact.substring(0, 50)}...`);

      // Ensure memory directory exists
      await fs.ensureDir(this.memoryDir);

      // Check memory file size
      if (await this.isMemoryFileTooLarge()) {
        return ToolResult.warning(
          'Memory file is too large. Consider cleaning up old memories.',
          {
            metadata: {
              memoryFile: this.memoryFile,
              maxSize: this.maxMemorySize
            }
          }
        );
      }

      // Create memory entry
      const memoryEntry = this.createMemoryEntry(fact, category, importance, tags);

      // Add to memory file using enhanced section management
      await this.addToMemorySection(memoryEntry);

      // Create result content
      const content = this.formatMemoryResult(fact, category, importance, tags);
      
      // Create display content
      const displayContent = this.createDisplayContent(fact, category, importance, tags, memoryEntry);

      const metadata = {
        fact,
        category,
        importance,
        tags,
        timestamp: memoryEntry.timestamp,
        memoryFile: this.memoryFile,
        entryId: memoryEntry.id
      };

      logger.info(`Memory saved: ${category} - ${fact.substring(0, 30)}...`);

      return ToolResult.success(content, {
        displayContent,
        metadata
      });

    } catch (error) {
      logger.error('Failed to save memory:', error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Create memory entry object
   */
  createMemoryEntry(fact, category, importance, tags) {
    const timestamp = new Date().toISOString();
    const id = this.generateEntryId(timestamp, fact);
    
    return {
      id,
      timestamp,
      fact: fact.trim(),
      category,
      importance,
      tags: [...new Set(tags)], // Remove duplicates
      dateAdded: new Date().toLocaleDateString()
    };
  }

  /**
   * Generate unique entry ID
   */
  generateEntryId(timestamp, fact) {
    const hash = this.simpleHash(timestamp + fact);
    return `mem_${hash}`;
  }

  /**
   * Simple hash function
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Add entry to memory file
   */
  async addToMemoryFile(entry) {
    let content = '';
    
    // Read existing content if file exists
    if (await fs.pathExists(this.memoryFile)) {
      content = await fs.readFile(this.memoryFile, 'utf8');
    } else {
      // Create initial content
      content = this.createInitialMemoryContent();
    }

    // Find or create category section
    const categorySection = `## ${entry.category}`;
    const entryMarkdown = this.formatEntryAsMarkdown(entry);

    if (content.includes(categorySection)) {
      // Add to existing category
      const sectionIndex = content.indexOf(categorySection);
      const nextSectionIndex = content.indexOf('\n## ', sectionIndex + 1);
      
      if (nextSectionIndex === -1) {
        // Last section
        content += '\n' + entryMarkdown;
      } else {
        // Insert before next section
        content = content.slice(0, nextSectionIndex) + 
                 '\n' + entryMarkdown + 
                 content.slice(nextSectionIndex);
      }
    } else {
      // Create new category section
      content += '\n' + categorySection + '\n' + entryMarkdown;
    }

    // Write updated content
    await fs.writeFile(this.memoryFile, content, 'utf8');
  }

  /**
   * Create initial memory file content
   */
  createInitialMemoryContent() {
    return `# LLM CLI Memory

This file contains persistent memories for the LLM CLI assistant.

## General

`;
  }

  /**
   * Format entry as markdown
   */
  formatEntryAsMarkdown(entry) {
    const lines = [];
    
    lines.push(`### ${entry.dateAdded} - ${entry.importance.toUpperCase()}`);
    lines.push(`**ID:** ${entry.id}`);
    lines.push(`**Fact:** ${entry.fact}`);
    
    if (entry.tags.length > 0) {
      lines.push(`**Tags:** ${entry.tags.map(tag => `#${tag}`).join(', ')}`);
    }
    
    lines.push(`**Added:** ${entry.timestamp}`);
    lines.push('');

    return lines.join('\n');
  }

  /**
   * Check if memory file is too large
   */
  async isMemoryFileTooLarge() {
    if (!await fs.pathExists(this.memoryFile)) {
      return false;
    }

    const stats = await fs.stat(this.memoryFile);
    return stats.size > this.maxMemorySize;
  }

  /**
   * Format memory result for LLM
   */
  formatMemoryResult(fact, category, importance, tags) {
    const lines = [];
    
    lines.push('Memory saved successfully');
    lines.push(`Category: ${category}`);
    lines.push(`Importance: ${importance}`);
    lines.push(`Fact: ${fact}`);
    
    if (tags.length > 0) {
      lines.push(`Tags: ${tags.join(', ')}`);
    }

    return lines.join('\n');
  }

  /**
   * Create display content
   */
  createDisplayContent(fact, category, importance, tags, entry) {
    const lines = [];
    
    // Header
    lines.push('🧠 Memory Saved');
    lines.push(`📁 Category: ${category}`);
    lines.push(`⭐ Importance: ${importance}`);
    lines.push(`🆔 ID: ${entry.id}`);
    lines.push('');
    
    // Content
    lines.push('📝 Fact:');
    lines.push(`"${fact}"`);
    lines.push('');
    
    // Tags
    if (tags.length > 0) {
      lines.push('🏷️  Tags:');
      lines.push(tags.map(tag => `#${tag}`).join(', '));
      lines.push('');
    }
    
    // Metadata
    lines.push('ℹ️  Details:');
    lines.push(`   Added: ${entry.dateAdded}`);
    lines.push(`   Timestamp: ${entry.timestamp}`);
    lines.push(`   Memory file: ${this.memoryFile}`);

    return lines.join('\n');
  }

  /**
   * Search memories (utility method)
   */
  async searchMemories(query, options = {}) {
    const { category = null, importance = null, tags = [] } = options;
    
    if (!await fs.pathExists(this.memoryFile)) {
      return [];
    }

    const content = await fs.readFile(this.memoryFile, 'utf8');
    const memories = this.parseMemoriesFromContent(content);
    
    return memories.filter(memory => {
      // Text search
      if (query && !memory.fact.toLowerCase().includes(query.toLowerCase())) {
        return false;
      }
      
      // Category filter
      if (category && memory.category !== category) {
        return false;
      }
      
      // Importance filter
      if (importance && memory.importance !== importance) {
        return false;
      }
      
      // Tags filter
      if (tags.length > 0 && !tags.some(tag => memory.tags.includes(tag))) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * Parse memories from file content
   */
  parseMemoriesFromContent(content) {
    // This is a simplified parser - in practice, you'd want more robust parsing
    const memories = [];
    const lines = content.split('\n');
    
    let currentMemory = null;
    
    for (const line of lines) {
      if (line.startsWith('**ID:**')) {
        if (currentMemory) {
          memories.push(currentMemory);
        }
        currentMemory = { id: line.replace('**ID:**', '').trim() };
      } else if (line.startsWith('**Fact:**') && currentMemory) {
        currentMemory.fact = line.replace('**Fact:**', '').trim();
      } else if (line.startsWith('**Tags:**') && currentMemory) {
        const tagsStr = line.replace('**Tags:**', '').trim();
        currentMemory.tags = tagsStr.split(',').map(tag => tag.trim().replace('#', ''));
      }
    }
    
    if (currentMemory) {
      memories.push(currentMemory);
    }
    
    return memories;
  }

  /**
   * Get memory statistics
   */
  async getMemoryStats() {
    if (!await fs.pathExists(this.memoryFile)) {
      return {
        totalMemories: 0,
        fileSize: 0,
        categories: [],
        lastUpdated: null
      };
    }

    const stats = await fs.stat(this.memoryFile);
    const content = await fs.readFile(this.memoryFile, 'utf8');
    const memories = this.parseMemoriesFromContent(content);
    
    const categories = [...new Set(memories.map(m => m.category))];
    
    return {
      totalMemories: memories.length,
      fileSize: stats.size,
      categories,
      lastUpdated: stats.mtime
    };
  }

  /**
   * Add memory entry to the Gemini Added Memories section
   */
  async addToMemorySection(memoryEntry) {
    let content = '';

    // Read existing content
    if (await fs.pathExists(this.memoryFile)) {
      content = await fs.readFile(this.memoryFile, 'utf8');
    } else {
      content = this.createInitialMemoryContent();
    }

    // Find or create the Gemini Added Memories section
    const sectionIndex = content.indexOf(this.memorySectionHeader);

    if (sectionIndex === -1) {
      // Section doesn't exist, add it at the end
      if (!content.endsWith('\n')) {
        content += '\n';
      }
      content += '\n' + this.memorySectionHeader + '\n\n';
    }

    // Format entry for markdown
    const entryText = this.formatEntryForFile(memoryEntry);

    // Add to the memories section
    content += entryText + '\n';

    // Write back to file
    await fs.writeFile(this.memoryFile, content, 'utf8');
  }

  /**
   * Archive old memories to keep file size manageable
   */
  async archiveOldMemories(content) {
    // This is a placeholder for memory archiving
    // In a full implementation, this would:
    // 1. Identify old or low-importance memories
    // 2. Move them to an archive file
    // 3. Keep only recent and high-importance memories
    // 4. Return the cleaned content

    // For now, just return the content as-is
    return content;
  }
}
