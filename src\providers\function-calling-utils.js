import logger from '../utils/logger.js';

/**
 * Shared utilities for function calling across all providers
 */
export class FunctionCallingUtils {
  /**
   * Normalize tool definitions across different provider formats
   */
  static normalizeToolDefinition(tool, providerType = 'openai') {
    if (!tool || !tool.name) {
      throw new Error('Invalid tool definition: missing name');
    }

    const normalized = {
      name: tool.name,
      description: tool.description || '',
      parameters: tool.parameters || { type: 'object', properties: {}, required: [] }
    };

    // Ensure parameters have the correct structure
    if (!normalized.parameters.type) {
      normalized.parameters.type = 'object';
    }
    if (!normalized.parameters.properties) {
      normalized.parameters.properties = {};
    }
    if (!normalized.parameters.required) {
      normalized.parameters.required = [];
    }

    return normalized;
  }

  /**
   * Convert tool definitions between provider formats
   */
  static convertToolFormat(tools, fromFormat, toFormat) {
    if (fromFormat === toFormat) {
      return tools;
    }

    return tools.map(tool => {
      switch (toFormat) {
        case 'openai':
          return {
            type: 'function',
            function: {
              name: tool.name,
              description: tool.description,
              parameters: tool.parameters || tool.input_schema
            }
          };

        case 'anthropic':
          return {
            name: tool.name,
            description: tool.description,
            input_schema: tool.parameters || tool.function?.parameters
          };

        case 'deepseek':
          // DeepSeek uses OpenAI-compatible format
          return this.convertToolFormat([tool], fromFormat, 'openai')[0];

        default:
          logger.warn(`Unknown tool format: ${toFormat}`);
          return tool;
      }
    });
  }

  /**
   * Validate tool call arguments against schema
   */
  static validateToolArguments(toolCall, toolDefinition) {
    const errors = [];
    
    if (!toolCall.function || !toolCall.function.arguments) {
      errors.push('Tool call missing arguments');
      return { valid: false, errors };
    }

    const args = typeof toolCall.function.arguments === 'string'
      ? JSON.parse(toolCall.function.arguments)
      : toolCall.function.arguments;

    const schema = toolDefinition.parameters || toolDefinition.input_schema;
    
    if (!schema) {
      return { valid: true, errors: [] };
    }

    // Check required parameters
    if (schema.required) {
      for (const requiredParam of schema.required) {
        if (!(requiredParam in args)) {
          errors.push(`Missing required parameter: ${requiredParam}`);
        }
      }
    }

    // Validate parameter types
    if (schema.properties) {
      for (const [paramName, paramValue] of Object.entries(args)) {
        const paramSchema = schema.properties[paramName];
        if (paramSchema && paramSchema.type) {
          const validationError = this.validateParameterType(paramName, paramValue, paramSchema);
          if (validationError) {
            errors.push(validationError);
          }
        }
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Validate individual parameter type
   */
  static validateParameterType(paramName, value, schema) {
    const actualType = Array.isArray(value) ? 'array' : typeof value;
    const expectedType = schema.type;

    if (expectedType === 'integer' && !Number.isInteger(value)) {
      return `Parameter ${paramName} must be an integer, got ${actualType}`;
    }

    if (expectedType === 'number' && typeof value !== 'number') {
      return `Parameter ${paramName} must be a number, got ${actualType}`;
    }

    if (expectedType === 'string' && typeof value !== 'string') {
      return `Parameter ${paramName} must be a string, got ${actualType}`;
    }

    if (expectedType === 'boolean' && typeof value !== 'boolean') {
      return `Parameter ${paramName} must be a boolean, got ${actualType}`;
    }

    if (expectedType === 'array' && !Array.isArray(value)) {
      return `Parameter ${paramName} must be an array, got ${actualType}`;
    }

    if (expectedType === 'object' && (typeof value !== 'object' || Array.isArray(value) || value === null)) {
      return `Parameter ${paramName} must be an object, got ${actualType}`;
    }

    return null;
  }

  /**
   * Generate tool call ID
   */
  static generateToolCallId(prefix = 'call') {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Sanitize tool arguments for safe execution
   */
  static sanitizeToolArguments(args) {
    if (typeof args !== 'object' || args === null) {
      return {};
    }

    const sanitized = {};
    
    for (const [key, value] of Object.entries(args)) {
      // Remove potentially dangerous keys
      if (key.startsWith('__') || key.includes('prototype')) {
        logger.warn(`Skipping potentially dangerous parameter: ${key}`);
        continue;
      }

      // Sanitize string values
      if (typeof value === 'string') {
        // Remove null bytes and control characters
        sanitized[key] = value.replace(/[\x00-\x1F\x7F]/g, '');
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'string' ? item.replace(/[\x00-\x1F\x7F]/g, '') : item
        );
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Estimate token usage for tool calls
   */
  static estimateToolCallTokens(toolCalls) {
    let totalTokens = 0;

    for (const toolCall of toolCalls) {
      // Rough estimation: function name + arguments JSON
      const functionName = toolCall.function?.name || '';
      const args = JSON.stringify(toolCall.function?.arguments || {});
      
      // Approximate 4 characters per token
      totalTokens += Math.ceil((functionName.length + args.length) / 4);
      
      // Add overhead for tool call structure
      totalTokens += 10;
    }

    return totalTokens;
  }

  /**
   * Create standardized tool result format
   */
  static createToolResult(toolCallId, success, content, metadata = {}) {
    return {
      toolCallId,
      success,
      content,
      timestamp: new Date().toISOString(),
      metadata
    };
  }

  /**
   * Merge tool call histories from multiple sessions
   */
  static mergeToolCallHistories(histories) {
    const merged = [];
    
    for (const history of histories) {
      if (Array.isArray(history)) {
        merged.push(...history);
      }
    }

    // Sort by timestamp
    return merged.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  }

  /**
   * Analyze tool usage patterns
   */
  static analyzeToolUsage(toolCallHistory) {
    const analysis = {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      toolFrequency: {},
      averageExecutionTime: 0,
      errorPatterns: {}
    };

    let totalExecutionTime = 0;

    for (const session of toolCallHistory) {
      for (const result of session.results || []) {
        analysis.totalCalls++;
        
        if (result.success) {
          analysis.successfulCalls++;
        } else {
          analysis.failedCalls++;
          
          // Track error patterns
          const errorType = result.error?.split(':')[0] || 'Unknown';
          analysis.errorPatterns[errorType] = (analysis.errorPatterns[errorType] || 0) + 1;
        }

        // Track tool frequency
        const toolName = result.toolName || 'unknown';
        analysis.toolFrequency[toolName] = (analysis.toolFrequency[toolName] || 0) + 1;

        // Track execution time
        if (result.executionTime) {
          totalExecutionTime += result.executionTime;
        }
      }
    }

    if (analysis.totalCalls > 0) {
      analysis.successRate = (analysis.successfulCalls / analysis.totalCalls) * 100;
      analysis.averageExecutionTime = totalExecutionTime / analysis.totalCalls;
    }

    return analysis;
  }

  /**
   * Optimize tool calls for better performance
   */
  static optimizeToolCalls(toolCalls, options = {}) {
    const {
      maxConcurrent = 5,
      prioritizeByFrequency = false,
      toolFrequency = {}
    } = options;

    let optimized = [...toolCalls];

    // Limit concurrent calls
    if (optimized.length > maxConcurrent) {
      logger.debug(`Limiting tool calls from ${optimized.length} to ${maxConcurrent}`);
      optimized = optimized.slice(0, maxConcurrent);
    }

    // Prioritize by frequency if requested
    if (prioritizeByFrequency && Object.keys(toolFrequency).length > 0) {
      optimized.sort((a, b) => {
        const freqA = toolFrequency[a.function?.name] || 0;
        const freqB = toolFrequency[b.function?.name] || 0;
        return freqB - freqA; // Higher frequency first
      });
    }

    return optimized;
  }
}

export default FunctionCallingUtils;
