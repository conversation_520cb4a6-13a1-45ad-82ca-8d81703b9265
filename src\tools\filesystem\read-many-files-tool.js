import path from 'path';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel, ToolDefaults } from '../base/tool-types.js';
import fileService from '../utils/file-service.js';
import contentProcessor from '../utils/content-processor.js';
import logger from '../../utils/logger.js';

/**
 * ReadManyFilesTool - Reads and concatenates multiple files
 */
export class ReadManyFilesTool extends BaseTool {
  constructor() {
    super({
      name: 'read_many_files',
      description: 'Reads and concatenates multiple files with pattern matching and filtering',
      category: ToolCategory.FILESYSTEM,
      permissionLevel: PermissionLevel.MODERATE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          paths: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of file paths or glob patterns to read'
          },
          include: {
            type: 'array',
            items: { type: 'string' },
            description: 'Additional include patterns',
            default: []
          },
          exclude: {
            type: 'array',
            items: { type: 'string' },
            description: 'Exclusion patterns',
            default: []
          },
          recursive: {
            type: 'boolean',
            description: 'Whether to search recursively',
            default: true
          },
          use_default_excludes: {
            type: 'boolean',
            description: 'Whether to apply default exclusions (node_modules, .git, etc.)',
            default: true
          },
          respect_git_ignore: {
            type: 'boolean',
            description: 'Whether to respect .gitignore patterns',
            default: true
          },
          max_files: {
            type: 'integer',
            description: 'Maximum number of files to process',
            minimum: 1,
            maximum: 100,
            default: 50
          },
          max_file_size: {
            type: 'integer',
            description: 'Maximum file size in bytes',
            minimum: 1024,
            maximum: 10485760,
            default: 1048576
          },
          separator: {
            type: 'string',
            description: 'Separator between file contents',
            default: '\n---\n'
          }
        },
        required: ['paths']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate paths
    if (!params.paths) {
      errors.push('paths is required');
    } else if (!Array.isArray(params.paths)) {
      errors.push('paths must be an array');
    } else if (params.paths.length === 0) {
      errors.push('paths cannot be empty');
    } else {
      for (const pathItem of params.paths) {
        if (typeof pathItem !== 'string') {
          errors.push('all paths must be strings');
          break;
        }
      }
    }

    // Validate include patterns
    if (params.include && !Array.isArray(params.include)) {
      errors.push('include must be an array');
    }

    // Validate exclude patterns
    if (params.exclude && !Array.isArray(params.exclude)) {
      errors.push('exclude must be an array');
    }

    // Validate boolean parameters
    if (params.recursive !== undefined && typeof params.recursive !== 'boolean') {
      errors.push('recursive must be a boolean');
    }

    if (params.use_default_excludes !== undefined && typeof params.use_default_excludes !== 'boolean') {
      errors.push('use_default_excludes must be a boolean');
    }

    if (params.respect_git_ignore !== undefined && typeof params.respect_git_ignore !== 'boolean') {
      errors.push('respect_git_ignore must be a boolean');
    }

    // Validate numeric parameters
    if (params.max_files !== undefined) {
      if (!Number.isInteger(params.max_files) || params.max_files < 1 || params.max_files > 100) {
        errors.push('max_files must be an integer between 1 and 100');
      }
    }

    if (params.max_file_size !== undefined) {
      if (!Number.isInteger(params.max_file_size) || params.max_file_size < 1024 || params.max_file_size > 10485760) {
        errors.push('max_file_size must be an integer between 1024 and 10485760 (10MB)');
      }
    }

    // Validate separator
    if (params.separator !== undefined && typeof params.separator !== 'string') {
      errors.push('separator must be a string');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    // Consider risky if reading many files or large files
    return params.max_files > 20 || params.max_file_size > 5 * 1024 * 1024; // 5MB
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    const fileCount = params.max_files || 50;
    const maxSize = this.formatFileSize(params.max_file_size || 1048576);
    
    return `Read up to ${fileCount} files (max ${maxSize} each) matching patterns:\n${params.paths.join('\n')}`;
  }

  /**
   * Execute bulk file reading
   */
  async execute(params) {
    const {
      paths,
      include = [],
      exclude = [],
      recursive = true,
      use_default_excludes = true,
      respect_git_ignore = true,
      max_files = 50,
      max_file_size = 1048576, // 1MB
      separator = '\n---\n'
    } = params;

    try {
      logger.debug(`Reading multiple files with patterns: ${paths.join(', ')}`);

      // Collect all patterns
      const allPatterns = [...paths, ...include];
      
      // Find files matching patterns
      const foundFiles = await fileService.findFiles(allPatterns, {
        respectGitIgnore: respect_git_ignore
      });

      // Apply additional filtering
      let filteredFiles = foundFiles;

      // Apply exclude patterns
      if (exclude.length > 0) {
        filteredFiles = filteredFiles.filter(filePath => {
          const relativePath = path.relative(fileService.rootDirectory, filePath);
          return !exclude.some(pattern => fileService.matchesPattern(relativePath, pattern));
        });
      }

      // Filter by file size
      filteredFiles = filteredFiles.filter(filePath => {
        try {
          const stats = fileService.getStats(filePath);
          return stats.size <= max_file_size;
        } catch (error) {
          logger.warn(`Failed to get stats for ${filePath}:`, error.message);
          return false;
        }
      });

      // Limit number of files
      const limitedFiles = filteredFiles.slice(0, max_files);

      if (limitedFiles.length === 0) {
        return ToolResult.warning('No files found matching the specified patterns and criteria.');
      }

      // Process files
      const processedResult = await contentProcessor.processMultipleFiles(limitedFiles, {
        separator,
        includeMetadata: true
      });

      // Create result content
      const content = this.formatBulkReadResult(processedResult, {
        totalFound: foundFiles.length,
        totalFiltered: filteredFiles.length,
        totalProcessed: limitedFiles.length,
        patterns: paths
      });

      // Create display content
      const displayContent = this.createDisplayContent(processedResult, {
        totalFound: foundFiles.length,
        totalFiltered: filteredFiles.length,
        totalProcessed: limitedFiles.length,
        patterns: paths,
        options: params
      });

      const metadata = {
        patterns: paths,
        totalFound: foundFiles.length,
        totalFiltered: filteredFiles.length,
        totalProcessed: limitedFiles.length,
        totalSize: processedResult.metadata.totalSize,
        files: processedResult.metadata.files,
        options: {
          recursive,
          useDefaultExcludes: use_default_excludes,
          respectGitIgnore: respect_git_ignore,
          maxFiles: max_files,
          maxFileSize: max_file_size
        }
      };

      return ToolResult.success(content, {
        displayContent,
        metadata
      });

    } catch (error) {
      logger.error(`Bulk file reading failed:`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Format bulk read result for LLM
   */
  formatBulkReadResult(processedResult, summary) {
    const lines = [];
    
    lines.push(`Bulk file read results:`);
    lines.push(`Patterns: ${summary.patterns.join(', ')}`);
    lines.push(`Found: ${summary.totalFound} files`);
    lines.push(`Processed: ${summary.totalProcessed} files`);
    lines.push(`Total size: ${this.formatFileSize(processedResult.metadata.totalSize)}`);
    lines.push('');
    
    lines.push('Combined content:');
    lines.push(processedResult.content);

    return lines.join('\n');
  }

  /**
   * Create display content with file listing
   */
  createDisplayContent(processedResult, summary) {
    const lines = [];
    
    // Header
    lines.push(`📚 Bulk File Read`);
    lines.push(`🔍 Patterns: ${summary.patterns.join(', ')}`);
    lines.push(`📊 Found ${summary.totalFound} files, processed ${summary.totalProcessed}`);
    lines.push(`💾 Total size: ${this.formatFileSize(processedResult.metadata.totalSize)}`);
    lines.push('');

    // File listing
    lines.push('📄 Processed Files:');
    for (const fileInfo of processedResult.metadata.files) {
      const size = this.formatFileSize(fileInfo.metadata.size || 0);
      const status = fileInfo.metadata.error ? '❌' : '✅';
      lines.push(`  ${status} ${fileInfo.path} (${size})`);
      
      if (fileInfo.metadata.error) {
        lines.push(`     Error: ${fileInfo.metadata.error}`);
      }
    }

    lines.push('');
    lines.push('📝 Combined Content:');
    lines.push('─'.repeat(50));
    lines.push(processedResult.content);

    // Summary statistics
    if (summary.totalFound > summary.totalProcessed) {
      lines.push('');
      lines.push(`ℹ️  ${summary.totalFound - summary.totalProcessed} files were excluded due to size limits or filters`);
    }

    return lines.join('\n');
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  }

  /**
   * Categorize file type for statistics
   */
  categorizeFileType(filePath, metadata) {
    const ext = path.extname(filePath).toLowerCase();

    // Image files
    if (['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp'].includes(ext)) {
      return 'imageFiles';
    }

    // PDF files
    if (ext === '.pdf') {
      return 'pdfFiles';
    }

    // Text files (including code)
    if (['.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.css', '.html', '.xml', '.json', '.yml', '.yaml'].includes(ext)) {
      return 'textFiles';
    }

    return 'otherFiles';
  }

  /**
   * Enhanced file discovery with intelligent filtering
   */
  async discoverFilesIntelligently(patterns, options = {}) {
    const {
      respectGitIgnore = true,
      maxFiles = 50,
      maxFileSize = 1048576,
      prioritizeRecent = true
    } = options;

    try {
      // Use enhanced file service discovery
      const files = await fileService.discoverFiles({
        pattern: patterns.length === 1 ? patterns[0] : `{${patterns.join(',')}}`,
        respectGitIgnore,
        maxDepth: 10
      });

      // Filter by size
      const sizeFilteredFiles = files.filter(file => {
        try {
          const stats = fileService.getStats(file);
          return stats.size <= maxFileSize;
        } catch (error) {
          return false;
        }
      });

      // Prioritize files if requested
      if (prioritizeRecent) {
        sizeFilteredFiles.sort((a, b) => {
          try {
            const statsA = fileService.getStats(a);
            const statsB = fileService.getStats(b);
            return statsB.mtime.getTime() - statsA.mtime.getTime();
          } catch (error) {
            return 0;
          }
        });
      }

      return sizeFilteredFiles.slice(0, maxFiles);

    } catch (error) {
      logger.error('Intelligent file discovery failed:', error.message);
      return [];
    }
  }
}
