import { ToolState, PermissionLevel, ToolDefaults } from './tool-types.js';
import { ToolResult } from './tool-result.js';
import logger from '../../utils/logger.js';
import inquirer from 'inquirer';
import diffGenerator from '../utils/diff-generator.js';
import chalk from 'chalk';

/**
 * Abstract base class for all tools
 */
export class BaseTool {
  constructor(config = {}) {
    this.name = config.name || this.constructor.name;
    this.description = config.description || '';
    this.category = config.category || 'utility';
    this.permissionLevel = config.permissionLevel || PermissionLevel.SAFE;
    this.timeout = config.timeout || ToolDefaults.timeout;
    this.state = ToolState.PENDING;
    this.startTime = null;
    this.endTime = null;
    this.telemetryEnabled = config.telemetryEnabled !== false;
    this.metrics = {
      executionCount: 0,
      totalExecutionTime: 0,
      successCount: 0,
      errorCount: 0,
      lastExecuted: null
    };
  }

  /**
   * Get tool function definition for LLM
   * Must be implemented by subclasses
   */
  getFunctionDefinition() {
    throw new Error('getFunctionDefinition must be implemented by subclass');
  }

  /**
   * Validate tool parameters
   * Must be implemented by subclasses
   */
  validateParams(params) {
    throw new Error('validateParams must be implemented by subclass');
  }

  /**
   * Execute the tool
   * Must be implemented by subclasses
   */
  async execute(params) {
    throw new Error('execute must be implemented by subclass');
  }

  /**
   * Check if tool execution should be confirmed with user
   */
  shouldConfirmExecution(params) {
    return this.permissionLevel === PermissionLevel.DANGEROUS ||
           (this.permissionLevel === PermissionLevel.MODERATE && this.isRiskyOperation(params));
  }

  /**
   * Determine if operation is risky (to be overridden by subclasses)
   */
  isRiskyOperation(params) {
    return false;
  }

  /**
   * AI-powered content correction (to be overridden by subclasses that need it)
   */
  async ensureCorrectContent(content, context = {}) {
    // Default implementation returns content as-is
    // Subclasses can override this for AI-powered correction
    return content;
  }

  /**
   * Generate preview for confirmation (to be overridden by subclasses)
   */
  async generatePreview(params) {
    return {
      hasPreview: false,
      preview: `Execute ${this.name} with parameters:\n${JSON.stringify(params, null, 2)}`,
      metadata: {}
    };
  }

  /**
   * Get confirmation message for user
   */
  async getConfirmationMessage(params) {
    const preview = await this.generatePreview(params);

    if (preview.hasPreview) {
      return preview.preview;
    }

    return `Execute ${this.name} with the following parameters?\n${JSON.stringify(params, null, 2)}`;
  }

  /**
   * Request user confirmation with enhanced preview
   */
  async requestConfirmation(params) {
    try {
      const message = await this.getConfirmationMessage(params);

      console.log(chalk.yellow('\n⚠️  Confirmation Required'));
      console.log(chalk.gray('─'.repeat(50)));
      console.log(message);
      console.log(chalk.gray('─'.repeat(50)));

      const { confirmed } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirmed',
          message: chalk.bold('Do you want to proceed?'),
          default: false
        }
      ]);

      return confirmed;
    } catch (error) {
      logger.error('Failed to get confirmation:', error.message);
      return false;
    }
  }

  /**
   * Main execution flow with lifecycle management
   */
  async run(params) {
    this.startTime = Date.now();
    this.state = ToolState.VALIDATING;

    try {
      // Update metrics
      this.metrics.executionCount++;
      this.metrics.lastExecuted = new Date().toISOString();

      // Validation phase
      logger.debug(`Validating parameters for ${this.name}`);
      const validation = await this.validateParams(params);
      if (!validation.isValid) {
        this.state = ToolState.FAILED;
        this.metrics.errorCount++;
        return ToolResult.error(`Validation failed: ${validation.errors.join(', ')}`, {
          toolName: this.name,
          executionTime: this.getExecutionTime(),
          metrics: this.getMetrics()
        });
      }

      // Confirmation phase
      if (this.shouldConfirmExecution(params)) {
        this.state = ToolState.CONFIRMING;
        logger.debug(`Requesting confirmation for ${this.name}`);

        const confirmed = await this.requestConfirmation(params);
        if (!confirmed) {
          this.state = ToolState.CANCELLED;
          return ToolResult.warning('Operation cancelled by user', {
            toolName: this.name,
            executionTime: this.getExecutionTime(),
            metrics: this.getMetrics()
          });
        }
      }

      // Execution phase
      this.state = ToolState.EXECUTING;
      logger.debug(`Executing ${this.name}`);

      const result = await this.executeWithTimeout(params);

      this.state = ToolState.COMPLETED;
      this.endTime = Date.now();

      // Update success metrics
      this.metrics.successCount++;
      this.metrics.totalExecutionTime += this.getExecutionTime();

      // Add execution metadata
      result.toolName = this.name;
      result.executionTime = this.getExecutionTime();
      if (this.telemetryEnabled) {
        result.metadata = result.metadata || {};
        result.metadata.metrics = this.getMetrics();
      }

      logger.debug(`${this.name} completed successfully`);
      return result;

    } catch (error) {
      this.state = ToolState.FAILED;
      this.endTime = Date.now();
      this.metrics.errorCount++;

      logger.error(`${this.name} execution failed:`, error.message);
      return ToolResult.error(error, {
        toolName: this.name,
        executionTime: this.getExecutionTime(),
        metrics: this.getMetrics()
      });
    }
  }

  /**
   * Execute with timeout protection
   */
  async executeWithTimeout(params) {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Tool execution timed out after ${this.timeout}ms`));
      }, this.timeout);

      try {
        const result = await this.execute(params);
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Get execution time in milliseconds
   */
  getExecutionTime() {
    if (!this.startTime) return 0;
    const endTime = this.endTime || Date.now();
    return endTime - this.startTime;
  }

  /**
   * Get tool metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      averageExecutionTime: this.metrics.successCount > 0
        ? Math.round(this.metrics.totalExecutionTime / this.metrics.successCount)
        : 0,
      successRate: this.metrics.executionCount > 0
        ? Math.round((this.metrics.successCount / this.metrics.executionCount) * 100)
        : 0
    };
  }

  /**
   * Reset tool metrics
   */
  resetMetrics() {
    this.metrics = {
      executionCount: 0,
      totalExecutionTime: 0,
      successCount: 0,
      errorCount: 0,
      lastExecuted: null
    };
  }

  /**
   * Get tool info with enhanced metadata
   */
  getInfo() {
    return {
      name: this.name,
      description: this.description,
      category: this.category,
      permissionLevel: this.permissionLevel,
      state: this.state,
      executionTime: this.getExecutionTime(),
      metrics: this.telemetryEnabled ? this.getMetrics() : null,
      lastExecuted: this.metrics.lastExecuted
    };
  }

  /**
   * Validate tool configuration
   */
  validateConfiguration() {
    const errors = [];

    if (!this.name || typeof this.name !== 'string') {
      errors.push('Tool name must be a non-empty string');
    }

    if (!this.description || typeof this.description !== 'string') {
      errors.push('Tool description must be a non-empty string');
    }

    if (!Object.values(PermissionLevel).includes(this.permissionLevel)) {
      errors.push('Invalid permission level');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
