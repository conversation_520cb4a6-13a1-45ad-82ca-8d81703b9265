import fs from 'fs-extra';
import path from 'path';
import sharp from 'sharp';
// Import pdf-parse dynamically to avoid initialization issues
let pdfParse = null;
import { FileType, ToolDefaults } from '../base/tool-types.js';
import fileService from './file-service.js';
import logger from '../../utils/logger.js';

/**
 * Content processing utilities for different file types
 */
class ContentProcessor {
  constructor() {
    this.maxFileSize = ToolDefaults.maxFileSize;
  }

  /**
   * Process single file content based on type
   */
  async processSingleFileContent(filePath, options = {}) {
    const resolvedPath = fileService.validatePath(filePath);
    
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`File does not exist: ${filePath}`);
    }

    const stats = fs.statSync(resolvedPath);
    
    // Check file size
    if (stats.size > this.maxFileSize) {
      throw new Error(`File too large: ${stats.size} bytes (max: ${this.maxFileSize})`);
    }

    const fileType = fileService.getFileType(resolvedPath);
    const { offset = 0, limit = null } = options;

    switch (fileType) {
      case FileType.TEXT:
        return await this.processTextFile(resolvedPath, { offset, limit });
      
      case FileType.IMAGE:
        return await this.processImageFile(resolvedPath);
      
      case FileType.PDF:
        return await this.processPDFFile(resolvedPath);
      
      case FileType.DIRECTORY:
        throw new Error('Cannot process directory as file content');
      
      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }
  }

  /**
   * Process text file with optional pagination
   */
  async processTextFile(filePath, options = {}) {
    const { offset = 0, limit = null } = options;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      let processedLines = lines;
      
      if (offset > 0 || limit !== null) {
        const startIndex = Math.max(0, offset);
        const endIndex = limit !== null ? startIndex + limit : lines.length;
        processedLines = lines.slice(startIndex, endIndex);
      }
      
      const result = {
        content: processedLines.join('\n'),
        metadata: {
          totalLines: lines.length,
          processedLines: processedLines.length,
          startLine: offset + 1,
          endLine: offset + processedLines.length,
          encoding: 'utf8',
          size: Buffer.byteLength(content, 'utf8')
        }
      };

      return result;
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`File not found: ${filePath}`);
      } else if (error.code === 'EACCES') {
        throw new Error(`Permission denied: ${filePath}`);
      } else if (error.code === 'EISDIR') {
        throw new Error(`Path is a directory: ${filePath}`);
      } else {
        throw new Error(`Failed to read file: ${error.message}`);
      }
    }
  }

  /**
   * Process image file
   */
  async processImageFile(filePath) {
    try {
      const image = sharp(filePath);
      const metadata = await image.metadata();

      // Convert to base64 for embedding
      const buffer = fs.readFileSync(filePath);
      const base64 = buffer.toString('base64');
      const mimeType = this.getMimeType(path.extname(filePath));

      // Generate thumbnail for large images
      let thumbnail = null;
      if (metadata.width > 800 || metadata.height > 600) {
        try {
          const thumbnailBuffer = await image
            .resize(400, 300, { fit: 'inside', withoutEnlargement: true })
            .jpeg({ quality: 80 })
            .toBuffer();
          thumbnail = `data:image/jpeg;base64,${thumbnailBuffer.toString('base64')}`;
        } catch (error) {
          logger.warn('Failed to generate thumbnail:', error.message);
        }
      }

      const result = {
        content: `Image: ${path.basename(filePath)} (${metadata.width}x${metadata.height}, ${metadata.format.toUpperCase()})`,
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          channels: metadata.channels,
          size: buffer.length,
          density: metadata.density,
          hasAlpha: metadata.hasAlpha,
          colorSpace: metadata.space,
          base64: `data:${mimeType};base64,${base64}`,
          thumbnail: thumbnail,
          aspectRatio: metadata.width / metadata.height
        }
      };

      return result;

    } catch (error) {
      throw new Error(`Failed to process image: ${error.message}`);
    }
  }

  /**
   * Process PDF file
   */
  async processPDFFile(filePath) {
    try {
      const stats = fs.statSync(filePath);

      // Try to load pdf-parse dynamically
      if (!pdfParse) {
        try {
          const pdfParseModule = await import('pdf-parse');
          pdfParse = pdfParseModule.default;
        } catch (error) {
          logger.warn('pdf-parse not available, using basic PDF info');
        }
      }

      if (pdfParse) {
        // Full PDF processing with pdf-parse
        const buffer = fs.readFileSync(filePath);
        const data = await pdfParse(buffer);

        const result = {
          content: data.text || `PDF file: ${path.basename(filePath)} (no extractable text)`,
          metadata: {
            pages: data.numpages,
            info: data.info || { Title: path.basename(filePath) },
            size: stats.size,
            version: data.version || 'unknown',
            textLength: data.text ? data.text.length : 0,
            hasText: Boolean(data.text && data.text.trim().length > 0)
          }
        };

        return result;
      } else {
        // Fallback when pdf-parse is not available
        const result = {
          content: `PDF file: ${path.basename(filePath)} (PDF text extraction not available - install pdf-parse package for full PDF support)`,
          metadata: {
            pages: 'unknown',
            info: { Title: path.basename(filePath) },
            size: stats.size,
            version: 'unknown',
            hasText: false,
            textLength: 0
          }
        };

        return result;
      }

    } catch (error) {
      throw new Error(`Failed to process PDF: ${error.message}`);
    }
  }

  /**
   * Get MIME type for file extension
   */
  getMimeType(extension) {
    const mimeMap = {
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml',
      '.bmp': 'image/bmp'
    };
    
    return mimeMap[extension.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Process multiple files and concatenate content
   */
  async processMultipleFiles(filePaths, options = {}) {
    const { separator = '\n---\n', includeMetadata = true } = options;
    const results = [];
    let totalSize = 0;
    
    for (const filePath of filePaths) {
      try {
        const result = await this.processSingleFileContent(filePath, options);
        
        // Add file header
        const relativePath = path.relative(fileService.rootDirectory, filePath);
        const header = `--- ${relativePath} ---`;
        
        const processedResult = {
          filePath: relativePath,
          content: includeMetadata ? `${header}\n${result.content}` : result.content,
          metadata: result.metadata
        };
        
        results.push(processedResult);
        totalSize += result.metadata.size || 0;
        
      } catch (error) {
        logger.warn(`Failed to process file ${filePath}:`, error.message);
        results.push({
          filePath: path.relative(fileService.rootDirectory, filePath),
          content: `Error processing file: ${error.message}`,
          metadata: { error: error.message }
        });
      }
    }
    
    // Combine all content
    const combinedContent = results.map(r => r.content).join(separator);
    
    return {
      content: combinedContent,
      metadata: {
        totalFiles: filePaths.length,
        processedFiles: results.length,
        totalSize,
        files: results.map(r => ({
          path: r.filePath,
          metadata: r.metadata
        }))
      }
    };
  }

  /**
   * Extract text content from various file types
   */
  async extractText(filePath) {
    const result = await this.processSingleFileContent(filePath);
    return result.content;
  }

  /**
   * Get file summary information
   */
  async getFileSummary(filePath) {
    const resolvedPath = fileService.validatePath(filePath);
    const stats = fs.statSync(resolvedPath);
    const fileType = fileService.getFileType(resolvedPath);
    
    const summary = {
      path: path.relative(fileService.rootDirectory, resolvedPath),
      type: fileType,
      size: stats.size,
      modified: stats.mtime,
      created: stats.birthtime
    };

    if (fileType === FileType.TEXT) {
      try {
        const content = fs.readFileSync(resolvedPath, 'utf8');
        summary.lines = content.split('\n').length;
        summary.characters = content.length;
      } catch (error) {
        summary.error = error.message;
      }
    }

    return summary;
  }
}

export default new ContentProcessor();
