import axios from 'axios';
import logger from '../utils/logger.js';
import { getToolRegistry } from '../tools/index.js';

export class BaseProvider {
  constructor(config) {
    this.config = config;
    this.name = config.name;
    this.apiKey = config.apiKey;
    this.baseURL = config.baseURL;
    this.models = config.models;
    this.defaultModel = config.defaultModel;
    this.maxTokens = config.maxTokens || 4096;
    this.temperature = config.temperature || 0.7;
    this.supportsTools = config.supportsTools || false;
    this.toolRegistry = null;

    // Enhanced function calling configuration
    this.functionCallingConfig = {
      enabled: config.functionCalling?.enabled !== false,
      maxToolCalls: config.functionCalling?.maxToolCalls || 10,
      maxRecursionDepth: config.functionCalling?.maxRecursionDepth || 5,
      autoConfirm: config.functionCalling?.autoConfirm || false,
      safeMode: config.functionCalling?.safeMode !== false,
      parallelExecution: config.functionCalling?.parallelExecution || false,
      timeout: config.functionCalling?.timeout || 30000
    };

    this.toolCallHistory = [];
    this.recursionDepth = 0;
  }

  /**
   * Validate provider configuration
   */
  validateConfig() {
    if (!this.apiKey) {
      throw new Error(`API key is required for ${this.name}`);
    }
    
    if (!this.baseURL) {
      throw new Error(`Base URL is required for ${this.name}`);
    }
    
    if (!this.models || this.models.length === 0) {
      throw new Error(`Models list is required for ${this.name}`);
    }
    
    if (!this.defaultModel) {
      throw new Error(`Default model is required for ${this.name}`);
    }
  }

  /**
   * Get available models
   */
  getModels() {
    return this.models;
  }

  /**
   * Get default model
   */
  getDefaultModel() {
    return this.defaultModel;
  }

  /**
   * Create HTTP client with authentication
   */
  createHttpClient() {
    return axios.create({
      baseURL: this.baseURL,
      headers: this.getAuthHeaders(),
      timeout: 30000
    });
  }

  /**
   * Get authentication headers (to be implemented by subclasses)
   */
  getAuthHeaders() {
    throw new Error('getAuthHeaders must be implemented by subclass');
  }

  /**
   * Format messages for API (to be implemented by subclasses)
   */
  formatMessages(messages) {
    throw new Error('formatMessages must be implemented by subclass');
  }

  /**
   * Parse API response (to be implemented by subclasses)
   */
  parseResponse(response) {
    throw new Error('parseResponse must be implemented by subclass');
  }

  /**
   * Send chat completion request with tool support
   */
  async sendMessage(messages, options = {}) {
    try {
      this.validateConfig();

      // Initialize tools if supported and not already initialized
      if (this.supportsTools && !this.toolRegistry) {
        await this.initializeTools();
      }

      const client = this.createHttpClient();
      const formattedMessages = this.formatMessages(messages);

      const requestData = {
        model: options.model || this.defaultModel,
        messages: formattedMessages,
        max_tokens: options.maxTokens || this.maxTokens,
        temperature: options.temperature || this.temperature,
        stream: options.stream || false,
        ...this.getAdditionalParams(options)
      };

      // Add tools if supported and enabled
      if (this.supportsToolCalling() && options.enableTools !== false) {
        const tools = this.getAvailableTools();
        if (tools.length > 0) {
          requestData.tools = this.formatToolsForRequest(tools);
          requestData.tool_choice = options.toolChoice || 'auto';
        }
      }

      logger.apiRequest('POST', `${this.baseURL}/chat/completions`);
      logger.debug('Request data:', JSON.stringify(requestData, null, 2));

      const response = await client.post('/chat/completions', requestData);

      logger.apiResponse(response.status, response.data);

      const parsedResponse = this.parseResponse(response.data);

      // Enhanced tool call processing
      if (parsedResponse.toolCalls && parsedResponse.toolCalls.length > 0) {
        logger.debug(`Processing ${parsedResponse.toolCalls.length} tool calls (depth: ${this.recursionDepth})`);

        // Check recursion depth
        if (this.recursionDepth >= this.functionCallingConfig.maxRecursionDepth) {
          logger.warn(`Maximum recursion depth reached (${this.functionCallingConfig.maxRecursionDepth})`);
          parsedResponse.toolResults = parsedResponse.toolCalls.map(call => ({
            toolCallId: call.id,
            success: false,
            error: 'Maximum recursion depth exceeded'
          }));
          return parsedResponse;
        }

        // Check tool call limit
        if (parsedResponse.toolCalls.length > this.functionCallingConfig.maxToolCalls) {
          logger.warn(`Too many tool calls requested (${parsedResponse.toolCalls.length} > ${this.functionCallingConfig.maxToolCalls})`);
          parsedResponse.toolCalls = parsedResponse.toolCalls.slice(0, this.functionCallingConfig.maxToolCalls);
        }

        const toolResults = await this.processToolCallsEnhanced(parsedResponse.toolCalls, options);
        parsedResponse.toolResults = toolResults;

        // Add to history
        this.toolCallHistory.push({
          timestamp: new Date().toISOString(),
          toolCalls: parsedResponse.toolCalls,
          results: toolResults,
          recursionDepth: this.recursionDepth
        });

        // If auto-continue is enabled, send tool results back to the model
        if (options.autoContinueTools !== false && toolResults.some(r => r.success)) {
          const toolMessages = this.formatToolResultsAsMessages(parsedResponse.toolCalls, toolResults);
          const updatedMessages = [...messages,
            { role: 'assistant', content: parsedResponse.content, toolCalls: parsedResponse.toolCalls },
            ...toolMessages
          ];

          // Increment recursion depth for recursive call
          this.recursionDepth++;
          try {
            const result = await this.sendMessage(updatedMessages, { ...options, enableTools: true });
            this.recursionDepth--;
            return result;
          } catch (error) {
            this.recursionDepth--;
            throw error;
          }
        }
      }

      return parsedResponse;

    } catch (error) {
      logger.error(`${this.name} API error:`, error.message);

      if (error.response) {
        const { status, data } = error.response;
        logger.error(`HTTP ${status}:`, data);

        // Handle common HTTP errors
        switch (status) {
          case 401:
            throw new Error(`Authentication failed. Please check your ${this.name} API key.`);
          case 403:
            throw new Error(`Access forbidden. Your ${this.name} API key may not have the required permissions.`);
          case 429:
            throw new Error(`Rate limit exceeded. Please try again later.`);
          case 500:
            throw new Error(`${this.name} server error. Please try again later.`);
          default:
            throw new Error(`${this.name} API error: ${data.error?.message || data.message || 'Unknown error'}`);
        }
      } else if (error.code === 'ECONNABORTED') {
        throw new Error(`Request timeout. ${this.name} API is not responding.`);
      } else if (error.code === 'ENOTFOUND') {
        throw new Error(`Network error. Cannot reach ${this.name} API.`);
      } else {
        throw new Error(`${this.name} error: ${error.message}`);
      }
    }
  }

  /**
   * Enhanced tool call processing with sophisticated lifecycle
   */
  async processToolCallsEnhanced(toolCalls, options = {}) {
    const results = [];

    if (this.functionCallingConfig.parallelExecution && toolCalls.length > 1) {
      // Process tool calls in parallel
      logger.debug('Processing tool calls in parallel');
      const promises = toolCalls.map(toolCall => this.processSingleToolCall(toolCall, options));
      const parallelResults = await Promise.allSettled(promises);

      for (let i = 0; i < parallelResults.length; i++) {
        const result = parallelResults[i];
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            toolCallId: toolCalls[i].id,
            success: false,
            error: result.reason.message || 'Tool execution failed'
          });
        }
      }
    } else {
      // Process tool calls sequentially
      logger.debug('Processing tool calls sequentially');
      for (const toolCall of toolCalls) {
        try {
          const result = await this.processSingleToolCall(toolCall, options);
          results.push(result);
        } catch (error) {
          logger.error(`Tool call ${toolCall.id} failed:`, error.message);
          results.push({
            toolCallId: toolCall.id,
            success: false,
            error: error.message
          });
        }
      }
    }

    return results;
  }

  /**
   * Process a single tool call with enhanced validation and confirmation
   */
  async processSingleToolCall(toolCall, options = {}) {
    const startTime = Date.now();

    try {
      logger.debug(`Processing tool call: ${toolCall.function.name}`);

      // Parse arguments
      let args;
      try {
        args = typeof toolCall.function.arguments === 'string'
          ? JSON.parse(toolCall.function.arguments)
          : toolCall.function.arguments;
      } catch (error) {
        throw new Error(`Invalid tool arguments: ${error.message}`);
      }

      // Execute tool with timeout
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Tool execution timeout')), this.functionCallingConfig.timeout)
      );

      const executionPromise = this.toolRegistry.executeTool(toolCall.function.name, args, {
        autoConfirm: this.functionCallingConfig.autoConfirm,
        safeMode: this.functionCallingConfig.safeMode
      });

      const result = await Promise.race([executionPromise, timeoutPromise]);
      const executionTime = Date.now() - startTime;

      logger.debug(`Tool ${toolCall.function.name} completed in ${executionTime}ms`);

      return {
        toolCallId: toolCall.id,
        success: true,
        result: result,
        executionTime,
        toolName: toolCall.function.name
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      logger.error(`Tool ${toolCall.function.name} failed after ${executionTime}ms:`, error.message);

      return {
        toolCallId: toolCall.id,
        success: false,
        error: error.message,
        executionTime,
        toolName: toolCall.function.name
      };
    }
  }

  /**
   * Format tool results as messages for continuation
   */
  formatToolResultsAsMessages(toolCalls, toolResults) {
    return toolResults.map((result, index) => ({
      role: 'tool',
      tool_call_id: result.toolCallId,
      content: result.success ? result.result.getLLMContent() : `Error: ${result.error}`
    }));
  }

  /**
   * Get additional parameters for API request (to be overridden by subclasses)
   */
  getAdditionalParams(options) {
    return {};
  }

  /**
   * Test API connection
   */
  async testConnection() {
    try {
      const testMessage = [{ role: 'user', content: 'Hello' }];
      await this.sendMessage(testMessage, { maxTokens: 10 });
      return { success: true, message: `${this.name} connection successful` };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * Initialize tool support
   */
  async initializeTools() {
    if (this.supportsTools) {
      this.toolRegistry = getToolRegistry();
      if (!this.toolRegistry.initialized) {
        await this.toolRegistry.initialize();
      }
      logger.debug(`Tool support initialized for ${this.name}`);
    }
  }

  /**
   * Get available tools
   */
  getAvailableTools() {
    if (!this.supportsTools || !this.toolRegistry) {
      return [];
    }
    return this.toolRegistry.getFunctionDefinitions();
  }

  /**
   * Check if tool calling is supported
   */
  supportsToolCalling() {
    return this.supportsTools && this.toolRegistry && this.toolRegistry.initialized;
  }

  /**
   * Execute a tool call
   */
  async executeTool(toolName, parameters) {
    if (!this.supportsToolCalling()) {
      throw new Error(`Tool calling not supported by ${this.name}`);
    }

    logger.debug(`Executing tool: ${toolName}`);
    return await this.toolRegistry.executeTool(toolName, parameters);
  }

  /**
   * Process tool calls in response
   */
  async processToolCalls(toolCalls) {
    if (!this.supportsToolCalling()) {
      throw new Error(`Tool calling not supported by ${this.name}`);
    }

    const results = [];

    for (const toolCall of toolCalls) {
      try {
        const result = await this.executeTool(toolCall.function.name, toolCall.function.arguments);
        results.push({
          toolCallId: toolCall.id,
          result,
          success: true
        });
      } catch (error) {
        logger.error(`Tool execution failed: ${toolCall.function.name}`, error.message);
        results.push({
          toolCallId: toolCall.id,
          error: error.message,
          success: false
        });
      }
    }

    return results;
  }

  /**
   * Format tool calls for API request (to be overridden by subclasses)
   */
  formatToolsForRequest(tools) {
    return tools;
  }

  /**
   * Parse tool calls from response (to be overridden by subclasses)
   */
  parseToolCallsFromResponse(response) {
    return [];
  }

  /**
   * Get provider info
   */
  getInfo() {
    return {
      name: this.name,
      models: this.models,
      defaultModel: this.defaultModel,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      configured: !!this.apiKey,
      supportsTools: this.supportsTools,
      toolsInitialized: this.supportsToolCalling(),
      functionCalling: {
        enabled: this.functionCallingConfig.enabled,
        maxToolCalls: this.functionCallingConfig.maxToolCalls,
        maxRecursionDepth: this.functionCallingConfig.maxRecursionDepth,
        parallelExecution: this.functionCallingConfig.parallelExecution,
        safeMode: this.functionCallingConfig.safeMode
      },
      toolCallHistory: this.toolCallHistory.length,
      availableTools: this.toolRegistry ? this.toolRegistry.getAllTools().length : 0
    };
  }

  /**
   * Reset tool call state
   */
  resetToolCallState() {
    this.toolCallHistory = [];
    this.recursionDepth = 0;
    logger.debug(`Reset tool call state for ${this.name}`);
  }

  /**
   * Get tool call statistics
   */
  getToolCallStats() {
    const totalCalls = this.toolCallHistory.reduce((sum, entry) => sum + entry.toolCalls.length, 0);
    const successfulCalls = this.toolCallHistory.reduce((sum, entry) =>
      sum + entry.results.filter(r => r.success).length, 0);

    return {
      totalSessions: this.toolCallHistory.length,
      totalCalls,
      successfulCalls,
      successRate: totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0,
      averageCallsPerSession: this.toolCallHistory.length > 0 ? totalCalls / this.toolCallHistory.length : 0,
      maxRecursionDepthUsed: Math.max(0, ...this.toolCallHistory.map(h => h.recursionDepth))
    };
  }

  /**
   * Configure function calling settings
   */
  configureFunctionCalling(config) {
    this.functionCallingConfig = {
      ...this.functionCallingConfig,
      ...config
    };
    logger.debug(`Updated function calling config for ${this.name}:`, this.functionCallingConfig);
  }

  /**
   * Check if a specific tool is available
   */
  isToolAvailable(toolName) {
    if (!this.toolRegistry) return false;
    return this.toolRegistry.hasTool(toolName);
  }

  /**
   * Validate tool call before execution
   */
  validateToolCall(toolCall) {
    const errors = [];

    if (!toolCall.function) {
      errors.push('Tool call missing function definition');
      return { valid: false, errors };
    }

    if (!toolCall.function.name) {
      errors.push('Tool call missing function name');
      return { valid: false, errors };
    }

    if (!this.isToolAvailable(toolCall.function.name)) {
      errors.push(`Tool '${toolCall.function.name}' is not available`);
      return { valid: false, errors };
    }

    // Validate arguments
    try {
      const args = typeof toolCall.function.arguments === 'string'
        ? JSON.parse(toolCall.function.arguments)
        : toolCall.function.arguments;

      // Additional validation could be added here based on tool schema

    } catch (error) {
      errors.push(`Invalid tool arguments: ${error.message}`);
    }

    return { valid: errors.length === 0, errors };
  }
}
