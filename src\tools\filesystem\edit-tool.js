import fs from 'fs-extra';
import path from 'path';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import fileService from '../utils/file-service.js';
import diffGenerator from '../utils/diff-generator.js';
import logger from '../../utils/logger.js';

/**
 * EditTool - Performs precise text replacements in files
 */
export class EditTool extends BaseTool {
  constructor() {
    super({
      name: 'replace',
      description: 'Performs precise text replacements in files with validation and diff preview',
      category: ToolCategory.FILESYSTEM,
      permissionLevel: PermissionLevel.MODERATE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          file_path: {
            type: 'string',
            description: 'Absolute path to the file to edit'
          },
          old_string: {
            type: 'string',
            description: 'Exact text to replace (should include sufficient context for uniqueness)'
          },
          new_string: {
            type: 'string',
            description: 'Replacement text'
          },
          expected_replacements: {
            type: 'integer',
            description: 'Expected number of replacements (for validation)',
            minimum: 1,
            default: 1
          },
          create_backup: {
            type: 'boolean',
            description: 'Whether to create a backup before editing',
            default: true
          }
        },
        required: ['file_path', 'old_string', 'new_string']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate file_path
    if (!params.file_path) {
      errors.push('file_path is required');
    } else if (typeof params.file_path !== 'string') {
      errors.push('file_path must be a string');
    }

    // Validate old_string
    if (!params.old_string) {
      errors.push('old_string is required');
    } else if (typeof params.old_string !== 'string') {
      errors.push('old_string must be a string');
    }

    // Validate new_string
    if (params.new_string === undefined || params.new_string === null) {
      errors.push('new_string is required');
    } else if (typeof params.new_string !== 'string') {
      errors.push('new_string must be a string');
    }

    // Validate expected_replacements
    if (params.expected_replacements !== undefined) {
      if (!Number.isInteger(params.expected_replacements) || params.expected_replacements < 1) {
        errors.push('expected_replacements must be a positive integer');
      }
    }

    // Validate boolean parameters
    if (params.create_backup !== undefined && typeof params.create_backup !== 'boolean') {
      errors.push('create_backup must be a boolean');
    }

    // Check if file exists and is accessible
    if (params.file_path) {
      try {
        const resolvedPath = fileService.validatePath(params.file_path);
        
        if (!fileService.exists(resolvedPath)) {
          errors.push(`File does not exist: ${params.file_path}`);
        } else {
          const stats = fileService.getStats(resolvedPath);
          if (stats.isDirectory()) {
            errors.push(`Path is a directory, not a file: ${params.file_path}`);
          }
        }

        // Check if file should be ignored
        if (fileService.shouldIgnore(resolvedPath)) {
          errors.push(`File is in ignore list: ${params.file_path}`);
        }

      } catch (error) {
        errors.push(`Invalid path: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    try {
      const resolvedPath = fileService.validatePath(params.file_path);
      
      // Check if editing important files
      const importantFiles = [
        'package.json',
        'package-lock.json',
        '.gitignore',
        'tsconfig.json',
        'webpack.config.js'
      ];

      const fileName = path.basename(params.file_path);
      if (importantFiles.includes(fileName)) {
        return true;
      }

      // Check if replacement is large
      if (params.old_string.length > 1000 || params.new_string.length > 1000) {
        return true;
      }

      return false;

    } catch (error) {
      return true; // Assume risky if we can't validate
    }
  }

  /**
   * AI-powered content correction for edit operations
   */
  async ensureCorrectEdit(oldString, newString, context = {}) {
    const { filePath, fileExtension, currentContent } = context;

    // For now, return strings as-is
    // In a full implementation, this would use an AI service to:
    // - Validate the replacement makes sense in context
    // - Fix any syntax errors in the new string
    // - Ensure proper indentation and formatting
    // - Validate that the old string exists and is unique enough

    return { oldString, newString };
  }

  /**
   * Calculate exact occurrences of old_string in content
   */
  calculateOccurrences(content, oldString) {
    if (!oldString) return 0;

    let count = 0;
    let index = 0;

    while ((index = content.indexOf(oldString, index)) !== -1) {
      count++;
      index += oldString.length;
    }

    return count;
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    try {
      const resolvedPath = fileService.validatePath(params.file_path);
      const relativePath = path.relative(fileService.rootDirectory, resolvedPath);
      
      // Read current content and calculate occurrences
      const currentContent = fs.readFileSync(resolvedPath, 'utf8');
      const occurrences = this.countOccurrences(currentContent, params.old_string);
      
      if (occurrences === 0) {
        return `No occurrences of the specified text found in ${relativePath}`;
      }

      // Generate preview
      const newContent = currentContent.replaceAll(params.old_string, params.new_string);
      const preview = diffGenerator.createChangePreview(
        currentContent,
        newContent,
        relativePath,
        { maxLines: 20 }
      );

      return `Replace ${occurrences} occurrence(s) in ${relativePath}?\n\n${preview.preview}`;

    } catch (error) {
      return super.getConfirmationMessage(params);
    }
  }

  /**
   * Generate preview for confirmation (enhanced version)
   */
  async generatePreview(params) {
    const { file_path: filePath, old_string: oldString, new_string: newString, expected_replacements } = params;

    try {
      const resolvedPath = fileService.validatePath(filePath);
      const relativePath = path.relative(fileService.rootDirectory, resolvedPath);
      const currentContent = fs.readFileSync(resolvedPath, 'utf8');

      // Apply AI correction if needed
      const corrected = await this.ensureCorrectEdit(oldString, newString, {
        filePath: relativePath,
        fileExtension: path.extname(filePath),
        currentContent
      });

      // Calculate occurrences
      const occurrences = this.calculateOccurrences(currentContent, corrected.oldString);

      if (occurrences === 0) {
        return {
          hasPreview: true,
          preview: `❌ No occurrences found of the specified text in ${relativePath}`,
          metadata: { occurrences: 0, error: 'No matches found' }
        };
      }

      // Validate expected replacements
      if (expected_replacements && occurrences !== expected_replacements) {
        return {
          hasPreview: true,
          preview: `⚠️  Expected ${expected_replacements} replacement(s) but found ${occurrences} occurrence(s) in ${relativePath}`,
          metadata: {
            occurrences,
            expected: expected_replacements,
            error: 'Occurrence count mismatch'
          }
        };
      }

      // Generate preview of changes
      const newContent = currentContent.replaceAll(corrected.oldString, corrected.newString);
      const diffResult = diffGenerator.createChangePreview(currentContent, newContent, relativePath);

      const preview = [
        `Editing file: ${relativePath}`,
        `Found ${occurrences} occurrence(s) of the specified text`,
        expected_replacements ? `Expected: ${expected_replacements} replacement(s) ✓` : '',
        '',
        diffResult.preview
      ].filter(Boolean).join('\n');

      return {
        hasPreview: true,
        preview,
        metadata: {
          occurrences,
          expected: expected_replacements,
          filePath: relativePath,
          oldLength: corrected.oldString.length,
          newLength: corrected.newString.length,
          contentCorrected: corrected.oldString !== oldString || corrected.newString !== newString,
          diff: diffResult.diff,
          stats: diffResult.stats
        }
      };

    } catch (error) {
      return {
        hasPreview: false,
        preview: `Error generating preview: ${error.message}`,
        metadata: { error: error.message }
      };
    }
  }

  /**
   * Execute text replacement
   */
  async execute(params) {
    const {
      file_path: filePath,
      old_string: oldString,
      new_string: newString,
      expected_replacements = 1,
      create_backup = true
    } = params;

    try {
      const resolvedPath = fileService.validatePath(filePath);
      const relativePath = path.relative(fileService.rootDirectory, resolvedPath);
      
      logger.debug(`Editing file: ${relativePath}`);

      // Read current content
      const currentContent = fs.readFileSync(resolvedPath, 'utf8');
      
      // Count occurrences
      const actualOccurrences = this.countOccurrences(currentContent, oldString);
      
      if (actualOccurrences === 0) {
        return ToolResult.error(`No occurrences of the specified text found in ${relativePath}`);
      }

      // Validate expected replacements
      if (actualOccurrences !== expected_replacements) {
        return ToolResult.warning(
          `Expected ${expected_replacements} replacement(s) but found ${actualOccurrences}. ` +
          `Use expected_replacements: ${actualOccurrences} to proceed.`,
          {
            metadata: {
              actualOccurrences,
              expectedReplacements: expected_replacements,
              filePath: relativePath
            }
          }
        );
      }

      // Create backup if requested
      let backupPath = null;
      if (create_backup) {
        backupPath = `${resolvedPath}.backup.${Date.now()}`;
        fs.copyFileSync(resolvedPath, backupPath);
        logger.debug(`Created backup: ${backupPath}`);
      }

      // Perform replacement
      const newContent = currentContent.replaceAll(oldString, newString);
      
      // Ensure parent directory exists
      const parentDir = path.dirname(resolvedPath);
      fileService.ensureDir(parentDir);

      // Write updated content
      fs.writeFileSync(resolvedPath, newContent, 'utf8');

      // Generate diff
      const diff = diffGenerator.createChangePreview(currentContent, newContent, relativePath);

      // Create result content
      const resultContent = this.formatEditResult(
        relativePath,
        actualOccurrences,
        diff
      );

      // Create display content
      const displayContent = this.createDisplayContent(
        relativePath,
        actualOccurrences,
        diff,
        backupPath
      );

      const metadata = {
        filePath: relativePath,
        absolutePath: resolvedPath,
        replacements: actualOccurrences,
        backupPath,
        diff: diff.stats,
        oldStringLength: oldString.length,
        newStringLength: newString.length
      };

      logger.info(`Edited file: ${relativePath} (${actualOccurrences} replacements)`);

      return ToolResult.success(resultContent, {
        displayContent,
        metadata
      });

    } catch (error) {
      logger.error(`Failed to edit file ${filePath}:`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Count occurrences of a string in text
   */
  countOccurrences(text, searchString) {
    if (!searchString) return 0;
    
    let count = 0;
    let position = 0;
    
    while ((position = text.indexOf(searchString, position)) !== -1) {
      count++;
      position += searchString.length;
    }
    
    return count;
  }

  /**
   * Format edit result for LLM
   */
  formatEditResult(relativePath, replacements, diff) {
    const lines = [];
    
    lines.push(`Edited file: ${relativePath}`);
    lines.push(`Replacements made: ${replacements}`);
    
    if (diff && diff.hasChanges) {
      lines.push('');
      lines.push('Changes:');
      lines.push(`  +${diff.stats.additions} additions`);
      lines.push(`  -${diff.stats.deletions} deletions`);
    }

    return lines.join('\n');
  }

  /**
   * Create display content with diff preview
   */
  createDisplayContent(relativePath, replacements, diff, backupPath) {
    const lines = [];
    
    // Header
    lines.push(`✏️  Edited file: ${relativePath}`);
    lines.push(`Replacements made: ${replacements}`);
    
    if (backupPath) {
      lines.push(`Backup created: ${path.basename(backupPath)}`);
    }
    
    // Show diff
    if (diff && diff.hasChanges) {
      lines.push('');
      lines.push('📊 Changes Summary:');
      lines.push(`  +${diff.stats.additions} additions`);
      lines.push(`  -${diff.stats.deletions} deletions`);
      lines.push(`  ${diff.stats.unchanged} unchanged lines`);
      
      lines.push('');
      lines.push('🔍 Diff Preview:');
      lines.push(diff.preview);
    }

    return lines.join('\n');
  }

  /**
   * Ensure correct edit using AI validation
   */
  async ensureCorrectEdit(currentContent, oldString, newString, filePath) {
    // This is a placeholder for AI-powered edit validation
    // In a real implementation, this would use an AI service to ensure
    // the edit is contextually appropriate and maintains code integrity
    
    // Basic validation
    const occurrences = this.countOccurrences(currentContent, oldString);
    
    if (occurrences === 0) {
      throw new Error('Old string not found in file content');
    }

    // Check for potential issues
    const ext = path.extname(filePath).toLowerCase();
    
    if (['.js', '.ts', '.json'].includes(ext)) {
      // For code files, ensure we're not breaking syntax
      const newContent = currentContent.replaceAll(oldString, newString);
      
      if (ext === '.json') {
        try {
          JSON.parse(newContent);
        } catch (error) {
          throw new Error('Edit would result in invalid JSON');
        }
      }
    }

    return {
      isValid: true,
      occurrences,
      suggestions: []
    };
  }
}
