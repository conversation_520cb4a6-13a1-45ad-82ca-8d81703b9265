import logger from '../../utils/logger.js';
import { ToolCategory } from './tool-types.js';

/**
 * Central registry for all tools
 */
export class ToolRegistry {
  constructor() {
    this.tools = new Map();
    this.categories = new Map();
    this.initialized = false;
    this.discoveredTools = new Map();
    this.mcpServers = new Map();
    this.telemetryEnabled = true;
    this.globalMetrics = {
      totalExecutions: 0,
      totalErrors: 0,
      totalExecutionTime: 0,
      toolUsageStats: new Map()
    };
  }

  /**
   * Register a tool with enhanced validation
   */
  register(tool) {
    if (!tool || !tool.name) {
      throw new Error('Tool must have a name');
    }

    // Validate tool configuration
    const validation = tool.validateConfiguration ? tool.validateConfiguration() : { isValid: true, errors: [] };
    if (!validation.isValid) {
      throw new Error(`Tool ${tool.name} configuration invalid: ${validation.errors.join(', ')}`);
    }

    if (this.tools.has(tool.name)) {
      logger.warn(`Tool ${tool.name} is already registered, overwriting`);
    }

    this.tools.set(tool.name, tool);

    // Add to category
    const category = tool.category || ToolCategory.UTILITY;
    if (!this.categories.has(category)) {
      this.categories.set(category, new Set());
    }
    this.categories.get(category).add(tool.name);

    // Initialize tool usage stats
    if (this.telemetryEnabled) {
      this.globalMetrics.toolUsageStats.set(tool.name, {
        executions: 0,
        errors: 0,
        totalTime: 0,
        lastUsed: null
      });
    }

    logger.debug(`Registered tool: ${tool.name} (${category})`);
  }

  /**
   * Unregister a tool
   */
  unregister(toolName) {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return false;
    }

    this.tools.delete(toolName);
    
    // Remove from category
    const category = tool.category || ToolCategory.UTILITY;
    if (this.categories.has(category)) {
      this.categories.get(category).delete(toolName);
      if (this.categories.get(category).size === 0) {
        this.categories.delete(category);
      }
    }

    logger.debug(`Unregistered tool: ${toolName}`);
    return true;
  }

  /**
   * Get a tool by name
   */
  getTool(toolName) {
    return this.tools.get(toolName);
  }

  /**
   * Check if tool exists
   */
  hasTool(toolName) {
    return this.tools.has(toolName);
  }

  /**
   * Get all tools
   */
  getAllTools() {
    return Array.from(this.tools.values());
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category) {
    const toolNames = this.categories.get(category);
    if (!toolNames) {
      return [];
    }
    return Array.from(toolNames).map(name => this.tools.get(name));
  }

  /**
   * Get all categories
   */
  getCategories() {
    return Array.from(this.categories.keys());
  }

  /**
   * Get function definitions for all tools (for LLM)
   */
  getFunctionDefinitions() {
    return this.getAllTools().map(tool => tool.getFunctionDefinition());
  }

  /**
   * Get function definitions by category
   */
  getFunctionDefinitionsByCategory(category) {
    return this.getToolsByCategory(category).map(tool => tool.getFunctionDefinition());
  }

  /**
   * Execute a tool by name with enhanced telemetry
   */
  async executeTool(toolName, params) {
    const tool = this.getTool(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    const startTime = Date.now();
    logger.debug(`Executing tool: ${toolName}`);

    try {
      const result = await tool.run(params);

      // Update telemetry
      if (this.telemetryEnabled) {
        const executionTime = Date.now() - startTime;
        this.updateToolMetrics(toolName, executionTime, true);
      }

      return result;
    } catch (error) {
      // Update error metrics
      if (this.telemetryEnabled) {
        const executionTime = Date.now() - startTime;
        this.updateToolMetrics(toolName, executionTime, false);
      }
      throw error;
    }
  }

  /**
   * Get tool statistics
   */
  getStats() {
    const stats = {
      totalTools: this.tools.size,
      categories: {},
      tools: {}
    };

    // Category stats
    for (const [category, toolNames] of this.categories) {
      stats.categories[category] = toolNames.size;
    }

    // Individual tool stats
    for (const [name, tool] of this.tools) {
      stats.tools[name] = tool.getInfo();
    }

    return stats;
  }

  /**
   * Initialize registry with default tools
   */
  async initialize() {
    if (this.initialized) {
      return;
    }

    logger.info('Initializing tool registry...');
    
    try {
      // Import and register all tools
      await this.loadTools();
      
      this.initialized = true;
      logger.info(`Tool registry initialized with ${this.tools.size} tools`);
      
    } catch (error) {
      logger.error('Failed to initialize tool registry:', error.message);
      throw error;
    }
  }

  /**
   * Load all tools from their modules
   */
  async loadTools() {
    try {
      // Load tools directly to avoid circular imports
      const { LSTool } = await import('../filesystem/ls-tool.js');
      const { ReadFileTool } = await import('../filesystem/read-file-tool.js');
      const { WriteFileTool } = await import('../filesystem/write-file-tool.js');
      const { EditTool } = await import('../filesystem/edit-tool.js');
      const { GlobTool } = await import('../filesystem/glob-tool.js');
      const { GrepTool } = await import('../filesystem/grep-tool.js');
      const { ReadManyFilesTool } = await import('../filesystem/read-many-files-tool.js');
      const { ShellTool } = await import('../execution/shell-tool.js');
      const { WebFetchTool } = await import('../web/web-fetch-tool.js');
      const { WebSearchTool } = await import('../web/web-search-tool.js');
      const { MemoryTool } = await import('../memory/memory-tool.js');

      // Register all tools
      this.register(new LSTool());
      this.register(new ReadFileTool());
      this.register(new WriteFileTool());
      this.register(new EditTool());
      this.register(new GlobTool());
      this.register(new GrepTool());
      this.register(new ReadManyFilesTool());
      this.register(new ShellTool());
      this.register(new WebFetchTool());
      this.register(new WebSearchTool());
      this.register(new MemoryTool());

      logger.debug('All tools loaded successfully');
    } catch (error) {
      logger.error('Failed to load tools:', error.message);
      throw error;
    }
  }

  /**
   * Discover and register dynamic tools
   */
  async discoverDynamicTools() {
    try {
      // Import MCP managers
      const { default: toolDiscoveryManager } = await import('../mcp/discovered-tool.js');
      const { default: mcpServerManager } = await import('../mcp/discovered-mcp-tool.js');

      // Discover tools from configured discovery commands
      const discoveredTools = await toolDiscoveryManager.discoverTools();
      for (const tool of discoveredTools) {
        this.register(tool);
      }

      // Discover tools from MCP servers
      const mcpTools = await mcpServerManager.discoverTools();
      for (const tool of mcpTools) {
        // Only register if allowed
        if (mcpServerManager.isAllowed(tool.serverConfig.name, tool.name)) {
          this.register(tool);
        }
      }

      logger.debug(`Discovered ${discoveredTools.length} dynamic tools and ${mcpTools.length} MCP tools`);
    } catch (error) {
      logger.warn('Failed to discover dynamic tools:', error.message);
    }
  }

  /**
   * Update tool metrics for telemetry
   */
  updateToolMetrics(toolName, executionTime, success) {
    if (!this.telemetryEnabled) return;

    // Update global metrics
    this.globalMetrics.totalExecutions++;
    this.globalMetrics.totalExecutionTime += executionTime;
    if (!success) {
      this.globalMetrics.totalErrors++;
    }

    // Update tool-specific metrics
    const toolStats = this.globalMetrics.toolUsageStats.get(toolName);
    if (toolStats) {
      toolStats.executions++;
      toolStats.totalTime += executionTime;
      toolStats.lastUsed = new Date().toISOString();
      if (!success) {
        toolStats.errors++;
      }
    }
  }

  /**
   * Get telemetry data
   */
  getTelemetryData() {
    if (!this.telemetryEnabled) {
      return { telemetryEnabled: false };
    }

    const toolStats = {};
    for (const [toolName, stats] of this.globalMetrics.toolUsageStats) {
      toolStats[toolName] = {
        ...stats,
        averageTime: stats.executions > 0 ? Math.round(stats.totalTime / stats.executions) : 0,
        errorRate: stats.executions > 0 ? Math.round((stats.errors / stats.executions) * 100) : 0
      };
    }

    return {
      telemetryEnabled: true,
      global: {
        ...this.globalMetrics,
        averageExecutionTime: this.globalMetrics.totalExecutions > 0
          ? Math.round(this.globalMetrics.totalExecutionTime / this.globalMetrics.totalExecutions)
          : 0,
        errorRate: this.globalMetrics.totalExecutions > 0
          ? Math.round((this.globalMetrics.totalErrors / this.globalMetrics.totalExecutions) * 100)
          : 0
      },
      tools: toolStats
    };
  }

  /**
   * Clear all tools (for testing)
   */
  clear() {
    this.tools.clear();
    this.categories.clear();
    this.discoveredTools.clear();
    this.mcpServers.clear();
    this.initialized = false;
    this.resetTelemetry();
    logger.debug('Tool registry cleared');
  }

  /**
   * Reset telemetry data
   */
  resetTelemetry() {
    this.globalMetrics = {
      totalExecutions: 0,
      totalErrors: 0,
      totalExecutionTime: 0,
      toolUsageStats: new Map()
    };

    // Reinitialize tool stats
    for (const toolName of this.tools.keys()) {
      this.globalMetrics.toolUsageStats.set(toolName, {
        executions: 0,
        errors: 0,
        totalTime: 0,
        lastUsed: null
      });
    }
  }
}

// Export singleton instance
export default new ToolRegistry();
