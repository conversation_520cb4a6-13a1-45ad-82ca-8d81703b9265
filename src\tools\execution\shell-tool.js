import path from 'path';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import fileService from '../utils/file-service.js';
import processManager from '../utils/process-manager.js';
import logger from '../../utils/logger.js';

/**
 * ShellTool - Executes shell commands with sandboxing
 */
export class ShellTool extends BaseTool {
  constructor() {
    super({
      name: 'run_shell_command',
      description: 'Executes shell commands with sandboxing and safety checks',
      category: ToolCategory.EXECUTION,
      permissionLevel: PermissionLevel.DANGEROUS
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          command: {
            type: 'string',
            description: 'Shell command to execute'
          },
          description: {
            type: 'string',
            description: 'User-friendly description of what the command does',
            default: null
          },
          directory: {
            type: 'string',
            description: 'Working directory for command execution (relative to root)',
            default: null
          },
          timeout: {
            type: 'integer',
            description: 'Command timeout in milliseconds',
            minimum: 1000,
            maximum: 300000,
            default: 30000
          },
          stream_output: {
            type: 'boolean',
            description: 'Whether to stream output in real-time',
            default: false
          },
          capture_stderr: {
            type: 'boolean',
            description: 'Whether to capture stderr separately',
            default: true
          }
        },
        required: ['command']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate command
    if (!params.command) {
      errors.push('command is required');
    } else if (typeof params.command !== 'string') {
      errors.push('command must be a string');
    } else if (params.command.trim() === '') {
      errors.push('command cannot be empty');
    }

    // Validate description
    if (params.description !== undefined && params.description !== null) {
      if (typeof params.description !== 'string') {
        errors.push('description must be a string');
      }
    }

    // Validate directory
    if (params.directory !== undefined && params.directory !== null) {
      if (typeof params.directory !== 'string') {
        errors.push('directory must be a string');
      } else {
        try {
          const resolvedPath = fileService.validatePath(params.directory);
          if (!fileService.exists(resolvedPath)) {
            errors.push(`Directory does not exist: ${params.directory}`);
          } else {
            const stats = fileService.getStats(resolvedPath);
            if (!stats.isDirectory()) {
              errors.push(`Path is not a directory: ${params.directory}`);
            }
          }
        } catch (error) {
          errors.push(`Invalid directory: ${error.message}`);
        }
      }
    }

    // Validate timeout
    if (params.timeout !== undefined) {
      if (!Number.isInteger(params.timeout) || params.timeout < 1000 || params.timeout > 300000) {
        errors.push('timeout must be an integer between 1000 and 300000 milliseconds');
      }
    }

    // Validate boolean parameters
    if (params.stream_output !== undefined && typeof params.stream_output !== 'boolean') {
      errors.push('stream_output must be a boolean');
    }

    if (params.capture_stderr !== undefined && typeof params.capture_stderr !== 'boolean') {
      errors.push('capture_stderr must be a boolean');
    }

    // Validate command safety
    if (params.command) {
      try {
        processManager.validateCommand(params.command);
      } catch (error) {
        errors.push(`Command validation failed: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Always require confirmation for shell commands
   */
  shouldConfirmExecution(params) {
    return true;
  }

  /**
   * Generate preview for confirmation
   */
  async generatePreview(params) {
    const { command, description, directory, timeout } = params;

    try {
      // Extract root command for safety analysis
      const rootCommand = processManager.extractRootCommand(command);
      const isCommandSafe = processManager.isCommandSafe(rootCommand);

      // Determine working directory
      const workingDir = directory
        ? path.resolve(fileService.rootDirectory, directory)
        : fileService.rootDirectory;

      const relativePath = path.relative(fileService.rootDirectory, workingDir);

      // Check if we're in a git repository
      const isGitRepo = await processManager.isGitRepository(workingDir);

      const preview = [
        `🔧 Shell Command Execution`,
        ``,
        `Command: ${command}`,
        description ? `Description: ${description}` : '',
        `Working Directory: ${relativePath || '.'}`,
        `Timeout: ${timeout || 30000}ms`,
        ``,
        `Security Analysis:`,
        `  Root Command: ${rootCommand}`,
        `  Safety Status: ${isCommandSafe ? '✅ Whitelisted' : '⚠️  Not in whitelist'}`,
        `  Git Repository: ${isGitRepo ? '✅ Yes' : '❌ No'}`,
        ``,
        `⚠️  This command will be executed with full system access.`,
        `Please review carefully before proceeding.`
      ].filter(Boolean).join('\n');

      return {
        hasPreview: true,
        preview,
        metadata: {
          command,
          rootCommand,
          isCommandSafe,
          workingDir: relativePath,
          isGitRepo,
          timeout: timeout || 30000
        }
      };

    } catch (error) {
      return {
        hasPreview: false,
        preview: `Error analyzing command: ${error.message}`,
        metadata: { error: error.message }
      };
    }
  }

  /**
   * Get confirmation message with command details
   */
  getConfirmationMessage(params) {
    const lines = [];
    
    lines.push('Execute shell command?');
    lines.push('');
    lines.push(`Command: ${params.command}`);
    
    if (params.description) {
      lines.push(`Description: ${params.description}`);
    }
    
    if (params.directory) {
      lines.push(`Directory: ${params.directory}`);
    } else {
      lines.push(`Directory: ${path.relative(fileService.rootDirectory, process.cwd())}`);
    }
    
    lines.push(`Timeout: ${params.timeout || 30000}ms`);
    
    // Extract root command for safety warning
    const rootCommand = processManager.extractRootCommand(params.command);
    if (!processManager.isCommandSafe(rootCommand)) {
      lines.push('');
      lines.push('⚠️  WARNING: This command is not in the safe command list!');
    }

    return lines.join('\n');
  }

  /**
   * Execute shell command
   */
  async execute(params) {
    const {
      command,
      description = null,
      directory = null,
      timeout = 30000,
      stream_output = false,
      capture_stderr = true
    } = params;

    try {
      const workingDir = directory 
        ? fileService.validatePath(directory)
        : fileService.rootDirectory;

      logger.debug(`Executing command: ${command}`);
      logger.debug(`Working directory: ${workingDir}`);

      const options = {
        cwd: workingDir,
        timeout,
        env: process.env
      };

      let result;

      if (stream_output) {
        // Use streaming execution for real-time output
        result = await processManager.executeCommandStreaming(command, {
          ...options,
          onStdout: (data) => {
            process.stdout.write(data);
          },
          onStderr: (data) => {
            if (capture_stderr) {
              process.stderr.write(data);
            }
          }
        });
      } else {
        // Use regular execution
        result = await processManager.executeCommand(command, options);
      }

      // Create result content
      const content = this.formatCommandResult(result, {
        command,
        description,
        workingDir: path.relative(fileService.rootDirectory, workingDir)
      });

      // Create display content
      const displayContent = this.createDisplayContent(result, {
        command,
        description,
        workingDir: path.relative(fileService.rootDirectory, workingDir),
        streamOutput: stream_output
      });

      const metadata = {
        command,
        description,
        workingDir: path.relative(fileService.rootDirectory, workingDir),
        exitCode: result.exitCode,
        executionTime: result.executionTime,
        processId: result.processId,
        hasError: result.exitCode !== 0
      };

      if (result.exitCode === 0) {
        logger.info(`Command executed successfully: ${command}`);
        return ToolResult.success(content, { displayContent, metadata });
      } else {
        logger.warn(`Command failed with exit code ${result.exitCode}: ${command}`);
        return ToolResult.warning(content, { displayContent, metadata });
      }

    } catch (error) {
      logger.error(`Command execution failed: ${command}`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Format command result for LLM
   */
  formatCommandResult(result, context) {
    const lines = [];
    
    lines.push(`Command: ${context.command}`);
    if (context.description) {
      lines.push(`Description: ${context.description}`);
    }
    lines.push(`Directory: ${context.workingDir}`);
    lines.push(`Exit code: ${result.exitCode}`);
    lines.push(`Execution time: ${result.executionTime}ms`);
    lines.push('');

    if (result.stdout) {
      lines.push('STDOUT:');
      lines.push(result.stdout);
      lines.push('');
    }

    if (result.stderr) {
      lines.push('STDERR:');
      lines.push(result.stderr);
      lines.push('');
    }

    if (result.error) {
      lines.push('ERROR:');
      lines.push(result.error);
    }

    return lines.join('\n');
  }

  /**
   * Create display content with formatting
   */
  createDisplayContent(result, context) {
    const lines = [];
    
    // Header
    const status = result.exitCode === 0 ? '✅' : '❌';
    lines.push(`${status} Shell Command Execution`);
    lines.push(`📁 Directory: ${context.workingDir}`);
    lines.push(`⏱️  Execution time: ${result.executionTime}ms`);
    lines.push(`🔢 Exit code: ${result.exitCode}`);
    
    if (context.description) {
      lines.push(`📝 Description: ${context.description}`);
    }
    
    lines.push('');
    lines.push(`💻 Command: ${context.command}`);
    lines.push('');

    // Output sections
    if (result.stdout && result.stdout.trim()) {
      lines.push('📤 STDOUT:');
      lines.push('```');
      lines.push(result.stdout.trim());
      lines.push('```');
      lines.push('');
    }

    if (result.stderr && result.stderr.trim()) {
      lines.push('📥 STDERR:');
      lines.push('```');
      lines.push(result.stderr.trim());
      lines.push('```');
      lines.push('');
    }

    if (result.error) {
      lines.push('🚨 ERROR:');
      lines.push('```');
      lines.push(result.error);
      lines.push('```');
    }

    // System information
    if (result.exitCode !== 0) {
      lines.push('');
      lines.push('ℹ️  System Information:');
      const sysInfo = processManager.getSystemInfo();
      lines.push(`Platform: ${sysInfo.platform}`);
      lines.push(`Shell: ${sysInfo.shell}`);
    }

    return lines.join('\n');
  }

  /**
   * Get active processes for monitoring
   */
  getActiveProcesses() {
    return processManager.getActiveProcesses();
  }

  /**
   * Kill a specific process
   */
  killProcess(processId) {
    return processManager.killProcess(processId);
  }

  /**
   * Kill all active processes
   */
  killAllProcesses() {
    return processManager.killAllProcesses();
  }
}
