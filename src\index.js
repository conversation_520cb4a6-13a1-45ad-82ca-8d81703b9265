#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { interactiveCommand } from './cli/commands/interactive.js';
import { configCommand } from './cli/commands/config.js';
import { chatCommand, listModels } from './cli/commands/chat.js';
import { toolsCommand } from './cli/commands/tools.js';
import configManager from './config/config-manager.js';
import logger from './utils/logger.js';

const program = new Command();

// Set up the CLI
program
  .name('llm-cli')
  .description('A modern CLI terminal interface for interacting with multiple LLM providers')
  .version('1.0.0');

// Interactive mode (default)
program
  .command('interactive', { isDefault: true })
  .alias('i')
  .description('Start interactive mode with configuration screen')
  .action(interactiveCommand);

// Configuration commands
program
  .command('config')
  .alias('c')
  .description('Configure providers and settings')
  .option('-p, --provider <provider>', 'Configure specific provider (openai, anthropic, deepseek)')
  .option('-k, --api-key <key>', 'Set API key for provider')
  .option('-m, --model <model>', 'Set default model for provider')
  .option('-a, --set-active', 'Set provider as active')
  .option('-l, --list', 'List all providers')
  .option('-t, --test', 'Test provider connections')
  .option('-s, --status', 'Show configuration status')
  .option('-i, --interactive', 'Open interactive configuration')
  .action(configCommand);

// Chat commands
program
  .command('chat [message]')
  .description('Start chat or send a single message')
  .option('-m, --model <model>', 'Use specific model')
  .option('-s, --system <prompt>', 'Set system prompt')
  .option('-t, --temperature <temp>', 'Set temperature (0.0-2.0)', parseFloat)
  .option('--max-tokens <tokens>', 'Set max tokens', parseInt)
  .option('--tokens', 'Show token usage')
  .action(chatCommand);

// Models command
program
  .command('models')
  .description('List available models for active provider')
  .action(listModels);

// Provider management commands
program
  .command('providers')
  .description('Manage providers')
  .option('-l, --list', 'List all providers')
  .option('-t, --test', 'Test all configured providers')
  .action(async (options) => {
    if (options.list) {
      await configCommand({ list: true });
    } else if (options.test) {
      await configCommand({ test: true });
    } else {
      await configCommand({ list: true });
    }
  });

// Tools management commands
program
  .command('tools')
  .description('Manage and inspect available tools')
  .option('-l, --list', 'List all available tools')
  .option('-s, --stats', 'Show detailed tool statistics')
  .option('--telemetry', 'Show telemetry and performance data')
  .option('-c, --categories', 'List tool categories')
  .option('-t, --test', 'Test tool system')
  .option('-i, --init', 'Initialize tool system')
  .action(toolsCommand);

// Global options
program
  .option('-v, --verbose', 'Enable verbose logging')
  .option('-q, --quiet', 'Suppress non-error output')
  .hook('preAction', (thisCommand, actionCommand) => {
    const opts = thisCommand.opts();
    
    if (opts.verbose) {
      logger.setLevel('DEBUG');
    } else if (opts.quiet) {
      logger.setLevel('ERROR');
    }
  });

// Parse command line arguments
try {
  await program.parseAsync(process.argv);
} catch (error) {
  if (error.code === 'commander.unknownCommand') {
    console.log(chalk.red(`Unknown command: ${error.message}`));
    console.log(chalk.gray('Run "llm-cli --help" for available commands'));
    process.exit(1);
  } else if (error.code === 'commander.helpDisplayed') {
    // Help was displayed, exit normally
    process.exit(0);
  } else {
    logger.error('CLI error:', error.message);
    console.log(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error.message);
  console.log(chalk.red(`Fatal error: ${error.message}`));
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection:', reason);
  console.log(chalk.red(`Unhandled promise rejection: ${reason}`));
  process.exit(1);
});

// Handle SIGINT (Ctrl+C)
process.on('SIGINT', () => {
  console.log(chalk.yellow('\nGoodbye!'));
  process.exit(0);
});
