import { ResultType } from './tool-types.js';

/**
 * Standardized tool execution result
 */
export class ToolResult {
  constructor(options = {}) {
    this.type = options.type || ResultType.SUCCESS;
    this.content = options.content || '';
    this.displayContent = options.displayContent || null;
    this.metadata = options.metadata || {};
    this.error = options.error || null;
    this.timestamp = new Date().toISOString();
    this.toolName = options.toolName || '';
    this.executionTime = options.executionTime || 0;

    // Enhanced metadata support
    this.resultId = options.resultId || this.generateResultId();
    this.chainId = options.chainId || null;
    this.parentResultId = options.parentResultId || null;
    this.tags = options.tags || [];
    this.artifacts = options.artifacts || [];
    this.metrics = options.metrics || {};
    this.context = options.context || {};
  }

  /**
   * Create a success result
   */
  static success(content, options = {}) {
    return new ToolResult({
      type: ResultType.SUCCESS,
      content,
      ...options
    });
  }

  /**
   * Create an error result
   */
  static error(error, options = {}) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new ToolResult({
      type: ResultType.ERROR,
      content: errorMessage,
      error: error instanceof Error ? error : new Error(errorMessage),
      ...options
    });
  }

  /**
   * Create a warning result
   */
  static warning(content, options = {}) {
    return new ToolResult({
      type: ResultType.WARNING,
      content,
      ...options
    });
  }

  /**
   * Create an info result
   */
  static info(content, options = {}) {
    return new ToolResult({
      type: ResultType.INFO,
      content,
      ...options
    });
  }

  /**
   * Check if result is successful
   */
  isSuccess() {
    return this.type === ResultType.SUCCESS;
  }

  /**
   * Check if result is an error
   */
  isError() {
    return this.type === ResultType.ERROR;
  }

  /**
   * Get content for LLM consumption
   */
  getLLMContent() {
    if (this.isError()) {
      return `Error: ${this.content}`;
    }
    return this.content;
  }

  /**
   * Get content for user display
   */
  getDisplayContent() {
    return this.displayContent || this.content;
  }

  /**
   * Convert to JSON for serialization
   */
  toJSON() {
    return {
      type: this.type,
      content: this.content,
      displayContent: this.displayContent,
      metadata: this.metadata,
      error: this.error ? {
        message: this.error.message,
        stack: this.error.stack
      } : null,
      timestamp: this.timestamp,
      toolName: this.toolName,
      executionTime: this.executionTime
    };
  }

  /**
   * Create from JSON
   */
  static fromJSON(json) {
    const result = new ToolResult(json);
    if (json.error) {
      result.error = new Error(json.error.message);
      result.error.stack = json.error.stack;
    }
    return result;
  }

  /**
   * Generate unique result ID
   */
  generateResultId() {
    return `result_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add artifact to result
   */
  addArtifact(artifact) {
    this.artifacts.push({
      id: `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      ...artifact
    });
    return this;
  }

  /**
   * Add tag to result
   */
  addTag(tag) {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
    return this;
  }

  /**
   * Set chain ID for result chaining
   */
  setChainId(chainId) {
    this.chainId = chainId;
    return this;
  }

  /**
   * Set parent result for chaining
   */
  setParentResult(parentResultId) {
    this.parentResultId = parentResultId;
    return this;
  }

  /**
   * Add context information
   */
  addContext(key, value) {
    this.context[key] = value;
    return this;
  }

  /**
   * Get rich formatted output
   */
  getRichDisplay() {
    const sections = [];

    // Main content
    sections.push({
      type: 'content',
      data: this.getDisplayContent()
    });

    // Artifacts
    if (this.artifacts.length > 0) {
      sections.push({
        type: 'artifacts',
        data: this.artifacts
      });
    }

    // Metadata
    if (Object.keys(this.metadata).length > 0) {
      sections.push({
        type: 'metadata',
        data: this.metadata
      });
    }

    // Context
    if (Object.keys(this.context).length > 0) {
      sections.push({
        type: 'context',
        data: this.context
      });
    }

    return sections;
  }

  /**
   * Check if result is a warning
   */
  isWarning() {
    return this.type === ResultType.WARNING;
  }

  /**
   * Check if result is info
   */
  isInfo() {
    return this.type === ResultType.INFO;
  }
}
