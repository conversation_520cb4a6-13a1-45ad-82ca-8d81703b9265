import fs from 'fs-extra';
import path from 'path';
import { glob } from 'glob';
import ignore from 'ignore';
import mimeTypes from 'mime-types';
import { FileType, SupportedImageFormats, DefaultExclusions } from '../base/tool-types.js';
import logger from '../../utils/logger.js';

/**
 * File system operations service
 */
class FileService {
  constructor() {
    this.rootDirectory = process.cwd();
    this.gitIgnore = null;
    this.geminiIgnore = null;
    this.loadIgnoreFiles();
  }

  /**
   * Set root directory for file operations
   */
  setRootDirectory(directory) {
    const resolvedPath = path.resolve(directory);
    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`Directory does not exist: ${resolvedPath}`);
    }
    this.rootDirectory = resolvedPath;
    this.loadIgnoreFiles();
    logger.debug(`Root directory set to: ${this.rootDirectory}`);
  }

  /**
   * Load .gitignore and .geminiignore files with enhanced support
   */
  loadIgnoreFiles() {
    // Load .gitignore with parent directory support
    this.gitIgnore = this.loadGitIgnoreHierarchy();

    // Load .geminiignore
    const geminiIgnorePath = path.join(this.rootDirectory, '.geminiignore');
    if (fs.existsSync(geminiIgnorePath)) {
      const geminiIgnoreContent = fs.readFileSync(geminiIgnorePath, 'utf8');
      this.geminiIgnore = ignore().add(geminiIgnoreContent);
    }

    // Add default security patterns
    this.securityIgnore = ignore().add([
      '.env',
      '.env.*',
      '*.key',
      '*.pem',
      '*.p12',
      '*.pfx',
      'id_rsa',
      'id_dsa',
      'id_ecdsa',
      'id_ed25519',
      '*.secret',
      'secrets.json',
      'config/secrets.yml',
      '.aws/credentials',
      '.ssh/config'
    ]);
  }

  /**
   * Load git ignore files from current directory up to root
   */
  loadGitIgnoreHierarchy() {
    const ignoreInstance = ignore();
    let currentDir = this.rootDirectory;

    while (currentDir !== path.dirname(currentDir)) {
      const gitIgnorePath = path.join(currentDir, '.gitignore');
      if (fs.existsSync(gitIgnorePath)) {
        try {
          const gitIgnoreContent = fs.readFileSync(gitIgnorePath, 'utf8');
          ignoreInstance.add(gitIgnoreContent);
        } catch (error) {
          logger.warn(`Failed to load .gitignore from ${gitIgnorePath}:`, error.message);
        }
      }
      currentDir = path.dirname(currentDir);
    }

    return ignoreInstance;
  }

  /**
   * Validate and resolve path
   */
  validatePath(filePath) {
    if (!filePath) {
      throw new Error('Path is required');
    }

    let resolvedPath;
    if (path.isAbsolute(filePath)) {
      resolvedPath = filePath;
    } else {
      resolvedPath = path.resolve(this.rootDirectory, filePath);
    }

    // Ensure path is within root directory
    if (!resolvedPath.startsWith(this.rootDirectory)) {
      throw new Error(`Path is outside root directory: ${filePath}`);
    }

    return resolvedPath;
  }

  /**
   * Check if path should be ignored
   */
  shouldIgnore(filePath, respectGitIgnore = true) {
    const relativePath = path.relative(this.rootDirectory, filePath);

    // Check security patterns first (always respected)
    if (this.securityIgnore && this.securityIgnore.ignores(relativePath)) {
      return true;
    }

    // Check .geminiignore first (always respected)
    if (this.geminiIgnore && this.geminiIgnore.ignores(relativePath)) {
      return true;
    }

    // Check .gitignore if requested
    if (respectGitIgnore && this.gitIgnore && this.gitIgnore.ignores(relativePath)) {
      return true;
    }

    // Check for sensitive file patterns
    if (this.isSensitiveFile(filePath)) {
      return true;
    }

    // Check default exclusions
    for (const pattern of DefaultExclusions) {
      if (this.matchesPattern(relativePath, pattern)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Simple pattern matching
   */
  matchesPattern(filePath, pattern) {
    // Convert glob pattern to regex
    const regexPattern = pattern
      .replace(/\*\*/g, '.*')
      .replace(/\*/g, '[^/]*')
      .replace(/\?/g, '[^/]');
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(filePath);
  }

  /**
   * Get file type
   */
  getFileType(filePath) {
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      return FileType.DIRECTORY;
    }

    const ext = path.extname(filePath).toLowerCase();
    
    if (SupportedImageFormats.includes(ext)) {
      return FileType.IMAGE;
    }

    if (ext === '.pdf') {
      return FileType.PDF;
    }

    const mimeType = mimeTypes.lookup(filePath);
    if (mimeType && mimeType.startsWith('text/')) {
      return FileType.TEXT;
    }

    // Try to detect text files by reading first few bytes
    try {
      const buffer = fs.readFileSync(filePath, { encoding: null, flag: 'r' });
      const sample = buffer.slice(0, 1024);
      
      // Check for null bytes (binary indicator)
      for (let i = 0; i < sample.length; i++) {
        if (sample[i] === 0) {
          return FileType.BINARY;
        }
      }
      
      return FileType.TEXT;
    } catch (error) {
      return FileType.BINARY;
    }
  }

  /**
   * Read directory contents
   */
  async readDirectory(dirPath, options = {}) {
    const resolvedPath = this.validatePath(dirPath);
    const {
      ignorePatterns = [],
      respectGitIgnore = true,
      recursive = false
    } = options;

    if (!fs.existsSync(resolvedPath)) {
      throw new Error(`Directory does not exist: ${dirPath}`);
    }

    const stats = fs.statSync(resolvedPath);
    if (!stats.isDirectory()) {
      throw new Error(`Path is not a directory: ${dirPath}`);
    }

    const entries = [];
    const items = fs.readdirSync(resolvedPath);

    for (const item of items) {
      const itemPath = path.join(resolvedPath, item);
      const relativePath = path.relative(this.rootDirectory, itemPath);

      // Check if should be ignored
      if (this.shouldIgnore(itemPath, respectGitIgnore)) {
        continue;
      }

      // Check custom ignore patterns
      if (ignorePatterns.some(pattern => this.matchesPattern(relativePath, pattern))) {
        continue;
      }

      const itemStats = fs.statSync(itemPath);
      const entry = {
        name: item,
        path: itemPath,
        relativePath,
        type: itemStats.isDirectory() ? 'directory' : 'file',
        size: itemStats.size,
        modified: itemStats.mtime,
        created: itemStats.birthtime
      };

      entries.push(entry);

      // Recurse into subdirectories if requested
      if (recursive && itemStats.isDirectory()) {
        try {
          const subEntries = await this.readDirectory(itemPath, options);
          entries.push(...subEntries);
        } catch (error) {
          logger.warn(`Failed to read subdirectory ${itemPath}:`, error.message);
        }
      }
    }

    // Sort entries (directories first, then alphabetical)
    entries.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    return entries;
  }

  /**
   * Find files using glob patterns
   */
  async findFiles(patterns, options = {}) {
    const {
      cwd = this.rootDirectory,
      ignore: customIgnore = [],
      respectGitIgnore = true,
      caseSensitive = false
    } = options;

    const globOptions = {
      cwd,
      absolute: true,
      nodir: true,
      dot: false,
      ignore: [...DefaultExclusions, ...customIgnore]
    };

    if (!caseSensitive) {
      globOptions.nocase = true;
    }

    const files = [];
    
    for (const pattern of Array.isArray(patterns) ? patterns : [patterns]) {
      try {
        const matches = await glob(pattern, globOptions);
        files.push(...matches);
      } catch (error) {
        logger.warn(`Glob pattern failed: ${pattern}`, error.message);
      }
    }

    // Remove duplicates and filter ignored files
    const uniqueFiles = [...new Set(files)];
    const filteredFiles = uniqueFiles.filter(file => {
      return !this.shouldIgnore(file, respectGitIgnore);
    });

    return filteredFiles;
  }

  /**
   * Check if file exists
   */
  exists(filePath) {
    try {
      const resolvedPath = this.validatePath(filePath);
      return fs.existsSync(resolvedPath);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file stats
   */
  getStats(filePath) {
    const resolvedPath = this.validatePath(filePath);
    return fs.statSync(resolvedPath);
  }

  /**
   * Ensure directory exists
   */
  ensureDir(dirPath) {
    const resolvedPath = this.validatePath(dirPath);
    fs.ensureDirSync(resolvedPath);
    return resolvedPath;
  }

  /**
   * Check if file contains sensitive information
   */
  isSensitiveFile(filePath) {
    const fileName = path.basename(filePath).toLowerCase();
    const sensitivePatterns = [
      /password/i,
      /secret/i,
      /token/i,
      /credential/i,
      /private.*key/i,
      /\.env$/i,
      /config.*secret/i
    ];

    return sensitivePatterns.some(pattern => pattern.test(fileName));
  }

  /**
   * Enhanced file discovery with better filtering
   */
  async discoverFiles(options = {}) {
    const {
      pattern = '**/*',
      respectGitIgnore = true,
      includeHidden = false,
      maxDepth = 10,
      fileTypes = null
    } = options;

    try {
      const globOptions = {
        cwd: this.rootDirectory,
        absolute: true,
        dot: includeHidden,
        maxDepth,
        ignore: respectGitIgnore ? this.getIgnorePatterns() : []
      };

      const files = await glob(pattern, globOptions);

      return files.filter(file => {
        // Additional filtering
        if (!includeHidden && path.basename(file).startsWith('.')) {
          return false;
        }

        if (fileTypes && fileTypes.length > 0) {
          const ext = path.extname(file).toLowerCase();
          if (!fileTypes.includes(ext)) {
            return false;
          }
        }

        return !this.shouldIgnore(file, respectGitIgnore);
      });

    } catch (error) {
      logger.error('File discovery failed:', error.message);
      return [];
    }
  }

  /**
   * Get ignore patterns for glob
   */
  getIgnorePatterns() {
    const patterns = [...DefaultExclusions];

    // Add git ignore patterns if available
    if (this.gitIgnore) {
      // Note: This is a simplified approach
      // In a full implementation, we'd need to convert ignore rules to glob patterns
      patterns.push('node_modules/**', '.git/**', 'dist/**', 'build/**');
    }

    return patterns;
  }

  /**
   * Check if directory is a git repository
   */
  isGitRepository(directory = this.rootDirectory) {
    return fs.existsSync(path.join(directory, '.git'));
  }

  /**
   * Get repository root directory
   */
  getRepositoryRoot(startDir = this.rootDirectory) {
    let currentDir = startDir;

    while (currentDir !== path.dirname(currentDir)) {
      if (fs.existsSync(path.join(currentDir, '.git'))) {
        return currentDir;
      }
      currentDir = path.dirname(currentDir);
    }

    return null;
  }
}

export default new FileService();
